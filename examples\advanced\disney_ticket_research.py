#!/usr/bin/env python3
"""
迪士尼门票信息研究示例

这个示例演示了如何使用CrawlerAgent自动完成复杂的多步骤任务：
1. 导航到Google香港
2. 搜索迪士尼乐园
3. 找到官网并点击
4. 提取门票价格和种类信息

使用最简单的方法：让CrawlerAgent自动选择合适的Agent和工具。
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def disney_ticket_research():
    """
    使用CrawlerAgent自动研究迪士尼门票信息
    
    这个函数展示了如何用一个简单的任务描述，
    让CrawlerAgent自动选择合适的工具和Agent来完成复杂任务。
    """
    
    print("🎪 迪士尼门票信息自动研究")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent - 这是我们唯一需要的Agent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 定义完整的任务 - 让AI自动选择如何执行
        task_description = """
        我需要研究迪士尼乐园的门票信息，请按以下步骤自动执行：
        
        1. 导航到 https://www.baidu.com/
        2. 在搜索框中搜索"迪士尼乐园"
        3. 从搜索结果中找到并点击迪士尼官方网站链接
        4. 在官网中找到门票或购票相关的页面
        5. 提取门票的价格信息和门票种类
        
        请在执行过程中提供详细的步骤反馈，如果某个步骤遇到困难，请尝试其他方法。
        最终请整理并返回找到的门票信息。
        """
        
        print("\n🚀 开始执行自动化任务...")
        print("📋 任务描述：研究迪士尼乐园门票信息")
        print("🎯 让CrawlerAgent自动选择最佳的执行策略...")
        
        # 执行任务 - CrawlerAgent会自动选择使用BrowserAgent还是ElementAgent
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 任务执行完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 任务执行失败: {e}")
        logger.error(f"Disney ticket research error: {e}")
        return None


def main():
    """主函数"""
    
    print("🎪 迪士尼门票信息自动研究演示")
    print("=" * 60)
    print("这个演示将展示CrawlerAgent如何自动：")
    print("• 🌐 导航到baidu")
    print("• 🔍 搜索迪士尼乐园")
    print("• 🎯 智能识别官方网站")
    print("• 🖱️ 自动点击进入官网")
    print("• 📊 提取门票价格和种类信息")
    print("• 🤖 全程AI自动选择最佳策略")
    print("=" * 60)
    
    # 询问用户是否继续
    user_input = input("\n是否开始执行？(y/n): ").strip().lower()
    if user_input not in ['y', 'yes', '是', '好']:
        print("👋 任务已取消")
        return 0
    
    # 执行研究任务
    result = disney_ticket_research()
    
    if result:
        print("\n🎉 迪士尼门票信息研究完成！")
        print("\n💡 关键特点：")
        print("• ✅ 单一Agent调用 - 只需要创建一个CrawlerAgent")
        print("• ✅ 自然语言任务描述 - 无需编程技能")
        print("• ✅ 自动工具选择 - AI自动选择BrowserAgent或ElementAgent")
        print("• ✅ 智能错误恢复 - 遇到问题自动尝试其他方法")
        print("• ✅ 结构化结果输出 - 清晰的信息整理")
        
        return 0
    else:
        print("\n❌ 任务执行失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
