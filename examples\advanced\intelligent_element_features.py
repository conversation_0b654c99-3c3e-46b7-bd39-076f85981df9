#!/usr/bin/env python3
"""
Intelligent ElementAgent Features Demo

This example demonstrates the advanced intelligent features of ElementAgent
including form analysis, accessibility validation, and automation strategy suggestions.
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_element_agent
from iicrawlermcp.core.browser import get_global_browser
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_intelligent_form_analysis():
    """Test intelligent form analysis capabilities."""
    print("\n🧠 Testing Intelligent Form Analysis")
    print("-" * 40)
    
    element_agent = build_element_agent()
    browser = get_global_browser()
    
    try:
        # Navigate to a form page
        browser.navigate("https://httpbin.org/forms/post")
        
        # Test intelligent form analysis
        result = element_agent.invoke("Perform an intelligent analysis of all forms on this page")
        print(f"Form Analysis Result: {result['output'][:200]}...")
        
        # Test intelligent form filling suggestion
        result = element_agent.invoke("""
            Suggest how to intelligently fill this form with the following data:
            {"customer name": "John Doe", "telephone": "555-1234", "email": "<EMAIL>", "comments": "Test order"}
        """)
        print(f"Form Filling Suggestion: {result['output'][:200]}...")
        
    finally:
        element_agent.cleanup()


def test_accessibility_validation():
    """Test accessibility validation features."""
    print("\n♿ Testing Accessibility Validation")
    print("-" * 40)
    
    element_agent = build_element_agent()
    browser = get_global_browser()
    
    try:
        # Test on a simple page
        browser.navigate("https://httpbin.org/html")
        
        result = element_agent.invoke("Validate this page for accessibility issues and provide recommendations")
        print(f"Accessibility Analysis: {result['output'][:300]}...")
        
        # Test on a more complex page
        browser.navigate("https://httpbin.org/forms/post")
        
        result = element_agent.invoke("Check the accessibility of this form page")
        print(f"Form Accessibility: {result['output'][:300]}...")
        
    finally:
        element_agent.cleanup()


def test_structured_data_extraction():
    """Test structured data extraction capabilities."""
    print("\n📊 Testing Structured Data Extraction")
    print("-" * 40)
    
    element_agent = build_element_agent()
    browser = get_global_browser()
    
    try:
        # Navigate to a page with structured data
        browser.navigate("https://httpbin.org/html")
        
        result = element_agent.invoke("Extract all structured data from this page including tables, lists, and other organized content")
        print(f"Structured Data: {result['output'][:300]}...")
        
        # Test on a different page
        browser.navigate("https://www.w3.org/WAI/WCAG21/quickref/")
        
        result = element_agent.invoke("Find and analyze any structured data elements on this page")
        print(f"W3C Page Data: {result['output'][:300]}...")
        
    finally:
        element_agent.cleanup()


def test_automation_strategy_suggestions():
    """Test automation strategy suggestion capabilities."""
    print("\n🎯 Testing Automation Strategy Suggestions")
    print("-" * 40)
    
    element_agent = build_element_agent()
    browser = get_global_browser()
    
    try:
        # Navigate to a form page
        browser.navigate("https://httpbin.org/forms/post")
        
        # Test different automation scenarios
        scenarios = [
            "Fill out the customer information form with test data",
            "Click the submit button after filling the form",
            "Extract all form field labels and types",
            "Navigate to the next page after form submission",
            "Validate that all required fields are filled"
        ]
        
        for scenario in scenarios:
            print(f"\n📋 Scenario: {scenario}")
            
            # Use the intelligent method directly
            strategy = element_agent.suggest_automation_strategy(scenario)
            
            if 'error' not in strategy:
                print(f"  Recommended Approach: {', '.join(strategy['recommended_approach'])}")
                print(f"  Confidence Score: {strategy['confidence_score']}/100")
                if strategy['potential_challenges']:
                    print(f"  Challenges: {', '.join(strategy['potential_challenges'])}")
            else:
                print(f"  Error: {strategy['error']}")
        
    finally:
        element_agent.cleanup()


def test_intelligent_element_search():
    """Test intelligent element search and matching."""
    print("\n🔍 Testing Intelligent Element Search")
    print("-" * 40)
    
    element_agent = build_element_agent()
    browser = get_global_browser()
    
    try:
        # Navigate to a form page
        browser.navigate("https://httpbin.org/forms/post")
        
        # Test intelligent search queries
        search_queries = [
            "find the submit button",
            "locate the customer name input field",
            "find the email address field",
            "locate any dropdown or select elements",
            "find all required form fields"
        ]
        
        for query in search_queries:
            print(f"\n🔎 Query: {query}")
            result = element_agent.invoke(query)
            print(f"  Result: {result['output'][:150]}...")
        
    finally:
        element_agent.cleanup()


def test_comprehensive_page_analysis():
    """Test comprehensive page analysis combining all features."""
    print("\n🔬 Testing Comprehensive Page Analysis")
    print("-" * 40)
    
    element_agent = build_element_agent()
    browser = get_global_browser()
    
    try:
        # Navigate to a complex page
        browser.navigate("https://httpbin.org/forms/post")
        
        # Perform comprehensive analysis
        result = element_agent.invoke("""
            Perform a comprehensive analysis of this page including:
            1. Overall page structure and element summary
            2. Form analysis and automation potential
            3. Accessibility assessment
            4. Interactive elements identification
            5. Recommendations for automation
        """)
        
        print(f"Comprehensive Analysis: {result['output'][:500]}...")
        
    finally:
        element_agent.cleanup()


def main():
    """Run all intelligent feature demonstrations."""
    
    print("🧠 ElementAgent Intelligent Features Demo")
    print("=" * 50)
    
    try:
        # Validate configuration
        config.validate()
        print("✅ Configuration validated")
        
        # Run all tests
        test_intelligent_form_analysis()
        test_accessibility_validation()
        test_structured_data_extraction()
        test_automation_strategy_suggestions()
        test_intelligent_element_search()
        test_comprehensive_page_analysis()
        
        print("\n✅ All intelligent features demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.error(f"Intelligent features demo error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
