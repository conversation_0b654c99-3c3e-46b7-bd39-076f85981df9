#!/usr/bin/env python3
"""
演示脚本：可视化浏览器操作

这个脚本会打开一个可见的浏览器窗口，执行一系列操作，
让你能够看到实际的浏览器自动化过程。
"""

import sys
import os
import time

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents.crawler_agent import build_agent


def demo_visible_browser():
    """演示可视化浏览器操作"""
    
    print("🚀 启动可视化浏览器演示...")
    print("=" * 50)
    
    try:
        # 创建代理
        print("📱 创建智能代理...")
        agent = build_agent()
        
        # 执行演示任务
        print("🎯 执行演示任务...")
        task = """
        请执行以下操作，让我能看到浏览器的实际操作过程：
        1. 导航到 https://www.google.com
        2. 截图保存为 demo_google.png
        3. 等待3秒让我观察页面
        4. 尝试在搜索框中输入 'Python programming'
        5. 再等待3秒
        6. 执行JavaScript获取页面标题
        """
        
        print("🔄 开始执行任务...")
        print("💡 提示：你应该能看到浏览器窗口打开并执行操作")
        print("-" * 50)
        
        result = agent.invoke(task)
        
        print("-" * 50)
        print("✅ 任务执行完成!")
        print(f"📋 代理响应: {result.get('output', '无响应')}")
        
        # 清理
        agent.cleanup()
        print("🧹 清理完成")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


def demo_simple_operations():
    """演示简单的浏览器操作"""
    
    print("\n🔧 演示简单浏览器操作...")
    print("=" * 50)
    
    try:
        from iicrawlermcp.core.browser import get_global_browser, close_global_browser
        
        # 获取浏览器实例
        print("📱 创建浏览器实例...")
        browser = get_global_browser()
        print(f"🖥️  浏览器模式: {'Headless' if browser.headless else 'Visible'}")
        
        # 导航到页面
        print("🌐 导航到Google...")
        result = browser.navigate("https://www.google.com")
        print(f"✅ 导航结果: {result}")
        
        # 等待观察
        print("⏱️  等待5秒让你观察浏览器...")
        time.sleep(5)
        
        # 截图
        print("📸 截图...")
        screenshot_path = browser.screenshot("demo_simple.png")
        print(f"✅ 截图保存: {screenshot_path}")
        
        # 获取页面信息
        print("📄 获取页面信息...")
        page_info = browser.get_page_info()
        print(f"📋 页面标题: {page_info.get('title', 'Unknown')}")
        print(f"🔗 页面URL: {page_info.get('url', 'Unknown')}")
        
        # 再等待一会
        print("⏱️  再等待3秒...")
        time.sleep(3)
        
        # 关闭浏览器
        print("🔒 关闭浏览器...")
        close_global_browser()
        print("✅ 演示完成!")
        
    except Exception as e:
        print(f"❌ 简单演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🎭 iICrawlerMCP 可视化浏览器演示")
    print("=" * 60)
    
    # 检查配置
    from iicrawlermcp.core.config import config
    print(f"⚙️  当前配置:")
    print(f"   - HEADLESS: {config.HEADLESS}")
    print(f"   - BROWSER_TIMEOUT: {config.BROWSER_TIMEOUT}")
    print(f"   - OPENAI_MODEL: {config.OPENAI_MODEL}")
    print()
    
    if config.HEADLESS:
        print("⚠️  警告: 当前配置为headless模式，你看不到浏览器窗口")
        print("💡 要看到浏览器窗口，请在.env文件中设置 HEADLESS=false")
        print()
    
    # 选择演示模式
    print("请选择演示模式:")
    print("1. 智能代理演示 (复杂任务)")
    print("2. 简单操作演示 (基础功能)")
    print("3. 两个都运行")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        demo_visible_browser()
    elif choice == "2":
        demo_simple_operations()
    elif choice == "3":
        demo_simple_operations()
        print("\n" + "="*60)
        demo_visible_browser()
    else:
        print("❌ 无效选择，运行简单演示...")
        demo_simple_operations()
