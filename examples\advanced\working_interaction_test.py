#!/usr/bin/env python3
"""
可工作的交互动作测试

基于之前的测试结果，使用正确的XPath格式进行交互测试。
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_working_form_interaction():
    """
    使用正确的XPath进行表单交互测试
    """
    
    print("✅ 可工作的表单交互测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 使用更具体和简单的任务描述
        task_description = """
        请执行以下表单交互任务：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 等待页面加载完成
        3. 填写基本信息：
           - 在第一个输入框（Customer name）输入 "测试用户"
           - 在第二个输入框（Telephone）输入 "13800138000"  
           - 在第三个输入框（E-mail）输入 "<EMAIL>"
        4. 选择Pizza Size为Large（使用单选按钮）
        5. 选择Bacon配料（使用复选框）
        6. 在配送说明文本框输入 "测试订单"
        7. 截取填写完成的表单截图
        
        请使用XPath定位器，格式如：html/body/form/fieldset[1]/p[3]/label/input
        不要使用CSS选择器如：html > body > form > p:nth-child(1)
        """
        
        print("\n🚀 开始可工作的表单交互测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Working interaction test error: {e}")
        return False


def test_step_by_step_interaction():
    """
    逐步进行交互测试
    """
    
    print("🔄 逐步交互测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 步骤1：导航和页面分析
        step1_task = """
        步骤1：页面导航和分析
        1. 导航到 https://httpbin.org/forms/post
        2. 等待页面完全加载
        3. 分析页面上的所有表单元素
        4. 截取初始页面截图
        """
        
        print("\n1️⃣ 执行步骤1：页面导航和分析...")
        result1 = crawler_agent.invoke(step1_task)
        print("步骤1结果：", result1['output'][:200] + "...")
        
        # 步骤2：填写文本字段
        step2_task = """
        步骤2：填写文本字段
        1. 在Customer name字段输入 "张三"
        2. 在Telephone字段输入 "13800138000"
        3. 在E-mail字段输入 "<EMAIL>"
        4. 截取填写后的截图
        
        请使用准确的XPath定位器。
        """
        
        print("\n2️⃣ 执行步骤2：填写文本字段...")
        result2 = crawler_agent.invoke(step2_task)
        print("步骤2结果：", result2['output'][:200] + "...")
        
        # 步骤3：选择单选按钮
        step3_task = """
        步骤3：选择Pizza Size
        1. 找到Pizza Size部分的Large单选按钮
        2. 点击选择Large选项
        3. 截取选择后的截图
        
        Large单选按钮的XPath应该是：html/body/form/fieldset[1]/p[3]/label/input
        """
        
        print("\n3️⃣ 执行步骤3：选择单选按钮...")
        result3 = crawler_agent.invoke(step3_task)
        print("步骤3结果：", result3['output'][:200] + "...")
        
        # 步骤4：选择复选框
        step4_task = """
        步骤4：选择Pizza Toppings
        1. 找到Pizza Toppings部分的Bacon复选框
        2. 点击选择Bacon选项
        3. 截取选择后的截图
        
        Bacon复选框的XPath应该是：html/body/form/fieldset[2]/p[1]/label/input
        """
        
        print("\n4️⃣ 执行步骤4：选择复选框...")
        result4 = crawler_agent.invoke(step4_task)
        print("步骤4结果：", result4['output'][:200] + "...")
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 逐步测试失败: {e}")
        logger.error(f"Step by step test error: {e}")
        return False


def main():
    """主函数"""
    
    print("🎮 可工作的交互动作测试")
    print("=" * 60)
    print("基于成功的直接测试结果，使用正确的XPath格式")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 完整表单交互测试")
    print("2. 逐步交互测试")
    
    choice = input("\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n✅ 执行完整表单交互测试...")
        result = test_working_form_interaction()
    elif choice == "2":
        print("\n🔄 执行逐步交互测试...")
        result = test_step_by_step_interaction()
    else:
        print("👋 无效选择，执行完整表单交互测试...")
        result = test_working_form_interaction()
    
    if result:
        print("\n🎉 交互测试成功完成！")
        print("\n💡 验证的功能：")
        print("• ✅ XPath选择器修复 - 正确处理XPath格式")
        print("• ✅ 文本输入 - 表单字段填写")
        print("• ✅ 单选按钮 - Pizza大小选择")
        print("• ✅ 复选框 - 配料选择")
        print("• ✅ 页面截图 - 操作过程记录")
        print("• ✅ Agent协作 - CrawlerAgent智能委托")
        
        return 0
    else:
        print("\n❌ 交互测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
