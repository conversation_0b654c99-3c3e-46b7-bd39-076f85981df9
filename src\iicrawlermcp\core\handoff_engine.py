"""
函数式移交执行引擎

支持Agent间的自动移交，基于OpenAI Swarm的设计理念
"""

import logging
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class HandoffResponse:
    """移交响应"""
    agent: Optional[Any] = None
    messages: list = None
    completed: bool = False
    output: str = ""


class HandoffEngine:
    """
    函数式移交执行引擎
    
    支持Agent间的自动移交，让LLM自主选择最合适的Agent处理任务
    """
    
    def __init__(self):
        self.max_handoffs = 5  # 最大移交次数，防止无限循环
        
    def execute_with_handoffs(self, initial_agent, user_input: str) -> HandoffResponse:
        """
        执行支持移交的任务
        
        Args:
            initial_agent: 初始Agent
            user_input: 用户输入
            
        Returns:
            HandoffResponse: 执行结果
        """
        current_agent = initial_agent
        handoff_count = 0
        
        logger.info(f"🚀 开始执行任务: {user_input}")
        logger.info(f"🤖 初始Agent: {type(current_agent).__name__}")
        
        try:
            while handoff_count < self.max_handoffs:
                logger.info(f"📍 执行轮次 {handoff_count + 1}")
                
                # 执行当前Agent
                result = self._execute_agent(current_agent, user_input)
                
                # 检查结果类型
                if self._is_agent_handoff(result):
                    # 发生移交
                    new_agent = self._extract_agent_from_result(result)
                    if new_agent and new_agent != current_agent:
                        handoff_count += 1
                        logger.info(f"🔄 移交 {handoff_count}: {type(current_agent).__name__} → {type(new_agent).__name__}")
                        current_agent = new_agent
                        continue
                
                # 任务完成
                output = self._extract_output(result)
                logger.info(f"✅ 任务完成，总移交次数: {handoff_count}")
                
                return HandoffResponse(
                    agent=current_agent,
                    completed=True,
                    output=output
                )
            
            # 达到最大移交次数
            logger.warning(f"⚠️ 达到最大移交次数 ({self.max_handoffs})，强制结束")
            return HandoffResponse(
                agent=current_agent,
                completed=False,
                output="任务因达到最大移交次数而终止"
            )
            
        except Exception as e:
            logger.error(f"❌ 执行失败: {e}")
            return HandoffResponse(
                completed=False,
                output=f"执行失败: {str(e)}"
            )
    
    def _execute_agent(self, agent, user_input: str) -> Any:
        """执行单个Agent"""
        try:
            if hasattr(agent, 'invoke'):
                # LangChain Agent
                if isinstance(user_input, str):
                    return agent.invoke({"input": user_input})
                else:
                    return agent.invoke(user_input)
            else:
                # 其他类型的Agent
                return agent(user_input)
        except Exception as e:
            logger.error(f"Agent执行失败: {e}")
            raise
    
    def _is_agent_handoff(self, result: Any) -> bool:
        """检查结果是否包含Agent移交"""
        # 检查是否直接返回了Agent实例
        if self._is_agent_instance(result):
            return True
            
        # 检查字典结果中是否包含Agent
        if isinstance(result, dict):
            output = result.get('output', '')
            if isinstance(output, str) and '移交给' in output:
                return True
                
        return False
    
    def _is_agent_instance(self, obj: Any) -> bool:
        """检查对象是否是Agent实例"""
        if obj is None:
            return False
            
        # 检查是否有Agent的特征方法
        return hasattr(obj, 'invoke') or hasattr(obj, '__call__')
    
    def _extract_agent_from_result(self, result: Any) -> Optional[Any]:
        """从结果中提取Agent实例"""
        if self._is_agent_instance(result):
            return result
            
        # 如果是字典，尝试从中提取Agent
        if isinstance(result, dict):
            for key, value in result.items():
                if self._is_agent_instance(value):
                    return value
                    
        return None
    
    def _extract_output(self, result: Any) -> str:
        """从结果中提取输出文本"""
        if isinstance(result, str):
            return result
        elif isinstance(result, dict):
            return result.get('output', str(result))
        else:
            return str(result)


def execute_user_task(user_input: str) -> str:
    """
    用户任务执行入口 - 支持自动移交
    
    Args:
        user_input: 用户的自然语言请求
        
    Returns:
        任务执行结果
    """
    try:
        from ..agents import build_agent
        
        # 创建移交引擎
        engine = HandoffEngine()
        
        # 创建初始Agent
        initial_agent = build_agent()
        
        # 执行任务
        response = engine.execute_with_handoffs(initial_agent, user_input)
        
        if response.completed:
            return response.output
        else:
            return f"❌ 任务未完成: {response.output}"
            
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        return f"❌ 任务执行失败: {str(e)}"
