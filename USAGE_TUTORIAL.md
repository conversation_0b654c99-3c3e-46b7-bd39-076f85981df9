# 🚀 iICrawlerMCP使用教程

## 📋 目录
1. [快速开始](#快速开始)
2. [基础使用](#基础使用)
3. [高级用法](#高级用法)
4. [常用任务](#常用任务)
5. [<PERSON> Desktop集成](#claude-desktop集成)
6. [故障排除](#故障排除)

## 🎯 快速开始

### 最简单的方式 - 一键演示

```bash
# 运行完整演示（推荐新手）
python demo_mcp_client.py
```

这将自动启动服务器并演示所有功能，您可以看到完整的交互过程。

### 验证安装

```bash
# 检查基础功能
python -c "
import sys, os
sys.path.insert(0, 'src')
from dotenv import load_dotenv
load_dotenv()
from iicrawlermcp.agents import build_agent
agent = build_agent()
print('✅ iICrawlerMCP安装正常')
"
```

## 🔧 基础使用

### 1. 手动启动服务器

**终端1 - 启动服务器：**
```bash
python demo_mcp_server.py
```

**终端2 - 运行客户端：**
```bash
python simple_mcp_demo.py
```

### 2. 自定义客户端

创建您自己的自动化脚本：

```python
import asyncio
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def my_automation():
    server_params = StdioServerParameters(
        command="python",
        args=["demo_mcp_server.py"],
        cwd="."
    )
    
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 执行任务
            result = await session.call_tool(
                "intelligent_web_task",
                {"task_description": "打开google.com并搜索Python"}
            )
            print(result.content[0].text)

asyncio.run(my_automation())
```

## 🎯 常用任务

### 网站导航
```python
# 打开网站
await session.call_tool("intelligent_web_task", 
    {"task_description": "打开github.com"})

# 点击链接
await session.call_tool("intelligent_web_task", 
    {"task_description": "点击Sign in按钮"})

# 页面跳转
await session.call_tool("intelligent_web_task", 
    {"task_description": "返回上一页"})
```

### 搜索操作
```python
# Google搜索
await session.call_tool("intelligent_web_task", 
    {"task_description": "在Google搜索'Python web scraping'"})

# 百度搜索
await session.call_tool("intelligent_web_task", 
    {"task_description": "打开baidu.com并搜索'机器学习'"})
```

### 表单填写
```python
# 填写输入框
await session.call_tool("intelligent_web_task", 
    {"task_description": "在用户名输入框中输入'testuser'"})

# 选择下拉菜单
await session.call_tool("intelligent_web_task", 
    {"task_description": "在国家下拉菜单中选择'中国'"})

# 提交表单
await session.call_tool("intelligent_web_task", 
    {"task_description": "点击提交按钮"})
```

### 信息提取
```python
# 获取页面信息
await session.call_tool("intelligent_web_task", 
    {"task_description": "获取当前页面的标题和URL"})

# 提取链接
await session.call_tool("intelligent_web_task", 
    {"task_description": "获取页面中的所有链接"})

# 提取文本
await session.call_tool("intelligent_web_task", 
    {"task_description": "提取页面主要内容文本"})
```

### 截图和状态
```python
# 截图
await session.call_tool("take_screenshot", {})

# 查看状态
await session.call_tool("browser_status", {})

# 清理资源
await session.call_tool("cleanup_browser", {})
```

## 🔗 Claude Desktop集成

### 1. 配置文件位置
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

### 2. 配置内容
```json
{
  "mcpServers": {
    "iicrawlermcp": {
      "command": "python",
      "args": ["path/to/iICrawlerMCP/scripts/start_mcp_server.py"],
      "cwd": "path/to/iICrawlerMCP",
      "env": {
        "OPENAI_API_KEY": "your_api_key",
        "OPENAI_API_BASE": "your_api_base", 
        "OPENAI_MODEL": "your_model"
      }
    }
  }
}
```

### 3. 在Claude中使用
配置完成后，重启Claude Desktop，然后可以直接对话：

```
"请帮我打开google.com并搜索Python教程"
"请截图当前页面"
"请获取这个页面的所有链接"
```

## 🛠️ 故障排除

### 常见问题

**1. 连接失败**
```bash
# 检查环境变量
cat .env

# 验证配置
python -c "
import sys, os
sys.path.insert(0, 'src')
from dotenv import load_dotenv
load_dotenv()
from iicrawlermcp.core.config import config
config.validate()
"
```

**2. 模块导入错误**
```bash
# 确保在正确目录
pwd
# 应该显示: /path/to/iICrawlerMCP

# 检查Python路径
python -c "import sys; print(sys.path)"
```

**3. MCP依赖问题**
```bash
# 安装MCP
pip install mcp

# 检查版本
python -c "import mcp; print(mcp.__version__)"
```

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📚 更多示例

运行提供的示例文件：
```bash
# 基础示例
python my_mcp_example.py

# 常用任务示例  
python common_tasks_examples.py

# 交互式客户端
python interactive_mcp_client.py
```

## 🎉 总结

现在您可以：
1. ✅ 使用演示客户端快速体验功能
2. ✅ 创建自定义自动化脚本
3. ✅ 在Claude Desktop中集成使用
4. ✅ 执行各种网页自动化任务

开始您的智能网页自动化之旅吧！🚀
