#!/usr/bin/env python3
"""
ElementAgent Performance Test

This script tests the performance of ElementAgent on complex pages
with many elements to ensure it can handle real-world scenarios.
"""

import sys
import os
import time
import logging
from typing import List, Dict, Any

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_element_agent
from iicrawlermcp.core.browser import get_global_browser
from iicrawlermcp.core.config import config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PerformanceTest:
    """Performance testing class for ElementAgent."""
    
    def __init__(self):
        self.element_agent = None
        self.browser = None
        self.results = []
    
    def setup(self):
        """Set up test environment."""
        config.validate()
        self.browser = get_global_browser()
        self.element_agent = build_element_agent()
        logger.info("Performance test setup completed")
    
    def cleanup(self):
        """Clean up test environment."""
        if self.element_agent:
            self.element_agent.cleanup()
        logger.info("Performance test cleanup completed")
    
    def measure_time(self, func, *args, **kwargs):
        """Measure execution time of a function."""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
    
    def test_simple_page(self) -> Dict[str, Any]:
        """Test performance on a simple page."""
        print("\n🔍 Testing Simple Page Performance")
        print("-" * 40)
        
        # Navigate to simple page
        self.browser.navigate("https://httpbin.org/html")
        
        results = {}
        
        # Test 1: Page summary
        result, time_taken = self.measure_time(
            self.element_agent.invoke, 
            "Get a comprehensive summary of this page"
        )
        results['page_summary'] = time_taken
        print(f"Page summary: {time_taken:.2f}s")
        
        # Test 2: Find interactive elements
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Find all interactive elements"
        )
        results['interactive_elements'] = time_taken
        print(f"Interactive elements: {time_taken:.2f}s")
        
        return results
    
    def test_complex_form_page(self) -> Dict[str, Any]:
        """Test performance on a complex form page."""
        print("\n📝 Testing Complex Form Page Performance")
        print("-" * 40)
        
        # Navigate to form page
        self.browser.navigate("https://httpbin.org/forms/post")
        
        results = {}
        
        # Test 1: Form analysis
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Analyze all form elements and their types"
        )
        results['form_analysis'] = time_taken
        print(f"Form analysis: {time_taken:.2f}s")
        
        # Test 2: Smart element search
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Find the submit button"
        )
        results['smart_search'] = time_taken
        print(f"Smart search: {time_taken:.2f}s")
        
        # Test 3: Element hierarchy analysis
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Analyze the structure and hierarchy of form elements"
        )
        results['hierarchy_analysis'] = time_taken
        print(f"Hierarchy analysis: {time_taken:.2f}s")
        
        return results
    
    def test_ecommerce_page(self) -> Dict[str, Any]:
        """Test performance on a complex e-commerce page."""
        print("\n🛒 Testing E-commerce Page Performance")
        print("-" * 40)
        
        # Navigate to a complex page (Amazon homepage)
        self.browser.navigate("https://www.amazon.com")
        
        results = {}
        
        # Test 1: Page summary (many elements)
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Get a summary of this page's structure"
        )
        results['complex_page_summary'] = time_taken
        print(f"Complex page summary: {time_taken:.2f}s")
        
        # Test 2: Find specific elements
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Find all search-related elements"
        )
        results['search_elements'] = time_taken
        print(f"Search elements: {time_taken:.2f}s")
        
        # Test 3: Navigation elements
        result, time_taken = self.measure_time(
            self.element_agent.invoke,
            "Find all navigation and menu elements"
        )
        results['navigation_elements'] = time_taken
        print(f"Navigation elements: {time_taken:.2f}s")
        
        return results
    
    def test_repeated_operations(self) -> Dict[str, Any]:
        """Test performance of repeated operations."""
        print("\n🔄 Testing Repeated Operations Performance")
        print("-" * 40)
        
        # Use the form page for repeated tests
        self.browser.navigate("https://httpbin.org/forms/post")
        
        results = {}
        
        # Test repeated page summaries
        times = []
        for i in range(5):
            result, time_taken = self.measure_time(
                self.element_agent.invoke,
                "Get page summary"
            )
            times.append(time_taken)
            print(f"  Iteration {i+1}: {time_taken:.2f}s")
        
        results['repeated_summaries'] = {
            'times': times,
            'average': sum(times) / len(times),
            'min': min(times),
            'max': max(times)
        }
        
        print(f"Average time: {results['repeated_summaries']['average']:.2f}s")
        print(f"Min time: {results['repeated_summaries']['min']:.2f}s")
        print(f"Max time: {results['repeated_summaries']['max']:.2f}s")
        
        return results
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all performance tests."""
        print("🚀 ElementAgent Performance Testing")
        print("=" * 50)
        
        all_results = {}
        
        try:
            self.setup()
            
            # Run individual tests
            all_results['simple_page'] = self.test_simple_page()
            all_results['complex_form'] = self.test_complex_form_page()
            all_results['ecommerce_page'] = self.test_ecommerce_page()
            all_results['repeated_operations'] = self.test_repeated_operations()
            
            # Generate summary
            self.generate_summary(all_results)
            
        except Exception as e:
            print(f"\n❌ Performance test failed: {e}")
            logger.error(f"Performance test error: {e}")
            return {}
        
        finally:
            self.cleanup()
        
        return all_results
    
    def generate_summary(self, results: Dict[str, Any]):
        """Generate performance test summary."""
        print("\n📊 Performance Test Summary")
        print("=" * 50)
        
        # Collect all timing data
        all_times = []
        
        for test_name, test_results in results.items():
            if test_name == 'repeated_operations':
                continue
            
            print(f"\n{test_name.replace('_', ' ').title()}:")
            for operation, time_taken in test_results.items():
                print(f"  {operation}: {time_taken:.2f}s")
                all_times.append(time_taken)
        
        # Repeated operations summary
        if 'repeated_operations' in results:
            rep_results = results['repeated_operations']['repeated_summaries']
            print(f"\nRepeated Operations:")
            print(f"  Average: {rep_results['average']:.2f}s")
            print(f"  Range: {rep_results['min']:.2f}s - {rep_results['max']:.2f}s")
        
        # Overall statistics
        if all_times:
            print(f"\nOverall Statistics:")
            print(f"  Total operations: {len(all_times)}")
            print(f"  Average time: {sum(all_times) / len(all_times):.2f}s")
            print(f"  Fastest operation: {min(all_times):.2f}s")
            print(f"  Slowest operation: {max(all_times):.2f}s")
            
            # Performance assessment
            avg_time = sum(all_times) / len(all_times)
            if avg_time < 5:
                print(f"  ✅ Performance: Excellent (< 5s average)")
            elif avg_time < 10:
                print(f"  ✅ Performance: Good (< 10s average)")
            elif avg_time < 20:
                print(f"  ⚠️ Performance: Acceptable (< 20s average)")
            else:
                print(f"  ❌ Performance: Needs improvement (> 20s average)")


def main():
    """Run performance tests."""
    test_runner = PerformanceTest()
    results = test_runner.run_all_tests()
    
    if results:
        print("\n✅ Performance testing completed successfully!")
        return 0
    else:
        print("\n❌ Performance testing failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
