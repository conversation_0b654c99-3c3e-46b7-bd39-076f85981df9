"""
DOM element unified formatter using Pydantic models.

This module provides centralized formatting logic for all DOM elements,
ensuring consistent output format across all DOM tools.
"""

from typing import List
from ..models.element_models import (
    LinkElement, ButtonElement, InputElement, ActionableElement
)
from ..models.output_models import NavigationElement, FormElement, MediaElement
from ..models.base_models import ElementInfo
from .base_formatter import BaseFormatter


class DOMFormatter:
    """DOM元素统一格式化器"""
    
    @staticmethod
    def format_links(elements: List[ElementInfo]) -> str:
        """格式化链接元素"""
        if not elements:
            return "🔗 No visible link elements found on the page"

        link_models = [LinkElement.from_element_info(elem) for elem in elements]

        result_lines = [f"🔗 Found {len(link_models)} visible link elements:"]
        result_lines.extend([f"  {link.to_line()}" for link in link_models])
        return "\n".join(result_lines)
    
    @staticmethod
    def format_buttons(elements: List[ElementInfo]) -> str:
        """格式化按钮元素"""
        if not elements:
            return "🔘 No visible button elements found on the page"

        button_models = [ButtonElement.from_element_info(elem) for elem in elements]

        result_lines = [f"🔘 Found {len(button_models)} visible button elements:"]
        result_lines.extend([f"  {button.to_line()}" for button in button_models])
        return "\n".join(result_lines)
    
    @staticmethod
    def format_inputs(elements: List[ElementInfo]) -> str:
        """格式化输入框元素"""
        if not elements:
            return "📝 No visible input elements found on the page"

        input_models = [InputElement.from_element_info(elem) for elem in elements]

        result_lines = [f"📝 Found {len(input_models)} visible input elements:"]
        result_lines.extend([f"  {input.to_line()}" for input in input_models])
        return "\n".join(result_lines)
    
    @staticmethod
    def format_actionable_elements(elements: List[ElementInfo]) -> str:
        """格式化可操作元素"""
        if not elements:
            return "⚡ No actionable elements found on the page"

        actionable_models = [ActionableElement.from_element_info(elem) for elem in elements]

        result_lines = [f"⚡ Found {len(actionable_models)} actionable elements:"]
        result_lines.extend([f"  {elem.to_line()}" for elem in actionable_models])
        return "\n".join(result_lines)
    
    @staticmethod
    def format_navigation_elements(elements: List[ElementInfo]) -> str:
        """格式化导航元素"""
        if not elements:
            return "🧭 No navigation elements found on the page"

        nav_models = [NavigationElement.from_element_info(elem) for elem in elements]

        result_lines = [f"🧭 Found {len(nav_models)} navigation elements:"]
        for nav in nav_models:
            text = nav.text_content[:50] + "..." if len(nav.text_content) > 50 else nav.text_content
            result_lines.append(f"  [{nav.highlight_index}] {nav.nav_type} (Level {nav.level}): '{text}' xpath='{nav.xpath}'")
        
        return "\n".join(result_lines)
    
    @staticmethod
    def format_form_elements(elements: List[ElementInfo]) -> str:
        """格式化表单元素"""
        if not elements:
            return "📋 No form elements found on the page"

        form_models = [FormElement.from_element_info(elem) for elem in elements]

        result_lines = [f"📋 Found {len(form_models)} form elements:"]
        for form in form_models:
            action_info = f" -> {form.action}" if form.action else ""
            method_info = f" ({form.method})" if form.method != "GET" else ""
            result_lines.append(f"  [{form.highlight_index}] Form{action_info}{method_info} xpath='{form.xpath}'")
        
        return "\n".join(result_lines)
    
    @staticmethod
    def format_media_elements(elements: List[ElementInfo]) -> str:
        """格式化媒体元素"""
        if not elements:
            return "🎬 No media elements found on the page"

        media_models = [MediaElement.from_element_info(elem) for elem in elements]

        result_lines = [f"🎬 Found {len(media_models)} media elements:"]
        for media in media_models:
            alt_info = f" (alt: {media.alt})" if media.alt else ""
            src_info = f" src: {media.src[:50]}..." if len(media.src) > 50 else f" src: {media.src}" if media.src else ""
            result_lines.append(f"  [{media.highlight_index}] {media.media_type.title()}{alt_info}{src_info} xpath='{media.xpath}'")
        
        return "\n".join(result_lines)
    
    @staticmethod
    def format_content_elements(elements: List[ElementInfo]) -> str:
        """
        格式化内容元素 - 增强版

        按语义类型分类显示，包含丰富的元素信息和优先级排序
        """
        if not elements:
            return "📄 No content elements found on the page"

        # 按语义类型分类
        categorized_elements = DOMFormatter._categorize_content_elements(elements)

        # 构建结果
        result_lines = [f"📄 Found {len(elements)} content elements:"]
        result_lines.append("")

        # 按优先级顺序显示各类别
        category_order = [
            ('structural', '🏗️ 结构性内容'),
            ('headings', '📋 标题'),
            ('semantic_text', '✨ 重要文本'),
            ('code_tech', '💻 代码和技术'),
            ('quotes', '💬 引用和引文'),
            ('definitions', '📖 定义和术语'),
            ('time_data', '⏰ 时间和数据'),
            ('interactive', '🔄 交互式内容'),
            ('figures', '🖼️ 图表'),
            ('lists_tables', '📊 列表和表格'),
            ('semantic_containers', '📦 语义容器'),
            ('paragraphs', '📝 段落'),
            ('other', '🔹 其他内容')
        ]

        total_shown = 0
        for category_key, category_title in category_order:
            if category_key in categorized_elements:
                category_elements = categorized_elements[category_key]
                if category_elements:
                    result_lines.append(f"{category_title} ({len(category_elements)}个)")

                    # 按重要性排序
                    sorted_elements = DOMFormatter._sort_elements_by_importance(category_elements)

                    for elem in sorted_elements:
                        formatted_line = DOMFormatter._format_single_content_element(elem, total_shown + 1)
                        result_lines.append(f"  {formatted_line}")
                        total_shown += 1

                    result_lines.append("")

        return "\n".join(result_lines)

    @staticmethod
    def _categorize_content_elements(elements: List[ElementInfo]) -> dict:
        """将内容元素按语义类型分类"""
        categories = {
            'structural': [],      # article, section, main, aside, header, footer
            'headings': [],        # h1-h6
            'semantic_text': [],   # strong, em, mark
            'code_tech': [],       # code, kbd, samp, var, pre
            'quotes': [],          # blockquote, q, cite
            'definitions': [],     # dfn, abbr, dl, dt, dd
            'time_data': [],       # time, data
            'interactive': [],     # details, summary
            'figures': [],         # figure, figcaption
            'lists_tables': [],    # ul, ol, table
            'semantic_containers': [], # semantic divs and spans
            'paragraphs': [],      # p
            'other': []           # 其他
        }

        for elem in elements:
            tag_name = elem.tag_name.lower()

            if tag_name in ['article', 'section', 'main', 'aside', 'header', 'footer']:
                categories['structural'].append(elem)
            elif tag_name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                categories['headings'].append(elem)
            elif tag_name in ['strong', 'em', 'mark']:
                categories['semantic_text'].append(elem)
            elif tag_name in ['code', 'kbd', 'samp', 'var', 'pre']:
                categories['code_tech'].append(elem)
            elif tag_name in ['blockquote', 'q', 'cite']:
                categories['quotes'].append(elem)
            elif tag_name in ['dfn', 'abbr', 'dl', 'dt', 'dd']:
                categories['definitions'].append(elem)
            elif tag_name in ['time', 'data']:
                categories['time_data'].append(elem)
            elif tag_name in ['details', 'summary']:
                categories['interactive'].append(elem)
            elif tag_name in ['figure', 'figcaption']:
                categories['figures'].append(elem)
            elif tag_name in ['ul', 'ol', 'table']:
                categories['lists_tables'].append(elem)
            elif tag_name == 'p':
                categories['paragraphs'].append(elem)
            elif tag_name in ['div', 'span']:
                categories['semantic_containers'].append(elem)
            else:
                categories['other'].append(elem)

        return categories

    @staticmethod
    def _sort_elements_by_importance(elements: List[ElementInfo]) -> List[ElementInfo]:
        """按重要性排序元素"""
        def get_importance_score(elem):
            tag_name = elem.tag_name.lower()
            score = 0

            # 标签本身的重要性
            tag_importance = {
                'h1': 10, 'h2': 9, 'h3': 8, 'h4': 7, 'h5': 6, 'h6': 5,
                'main': 10, 'article': 9, 'section': 8,
                'strong': 8, 'mark': 7, 'em': 6,
                'blockquote': 7, 'code': 6, 'pre': 6,
                'time': 5, 'dfn': 5, 'abbr': 4,
                'figure': 6, 'figcaption': 5,
                'table': 7, 'ul': 5, 'ol': 5,
                'p': 3, 'div': 2, 'span': 1
            }
            score += tag_importance.get(tag_name, 0)

            # div和span的特殊评分
            if tag_name in ['div', 'span']:
                score += DOMFormatter._get_semantic_element_score(elem)

            # 文本长度加分
            text_length = len(elem.text_content.strip())
            if text_length > 100:
                score += 2
            elif text_length > 50:
                score += 1

            return score

        return sorted(elements, key=get_importance_score, reverse=True)

    @staticmethod
    def _get_semantic_element_score(elem: ElementInfo) -> int:
        """获取div/span的语义重要性评分"""
        score = 0
        classes = elem.attributes.get('class', '').lower()
        id_attr = elem.attributes.get('id', '').lower()
        role = elem.attributes.get('role', '').lower()

        # 高重要性模式
        high_patterns = ['main', 'content', 'article', 'important', 'highlight', 'primary']
        if any(pattern in classes or pattern in id_attr for pattern in high_patterns):
            score += 3

        # 中等重要性模式
        medium_patterns = ['post', 'entry', 'description', 'summary', 'notice']
        if any(pattern in classes or pattern in id_attr for pattern in medium_patterns):
            score += 2

        # ARIA角色
        if role in ['article', 'main', 'complementary', 'note']:
            score += 2

        return score

    @staticmethod
    def _format_single_content_element(elem: ElementInfo, index: int) -> str:
        """格式化单个内容元素"""
        tag_name = elem.tag_name.lower()
        text = elem.text_content.strip()

        # 高亮索引
        highlight_info = f"[{elem.highlight_index}]" if elem.highlight_index is not None else f"[{index}]"

        # 根据不同标签类型进行特殊格式化
        if tag_name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
            # 标题元素
            display_text = text[:80] + "..." if len(text) > 80 else text
            return f"{highlight_info} {tag_name.upper()}: '{display_text}' xpath='{elem.xpath}'"

        elif tag_name in ['strong', 'em', 'mark']:
            # 重要文本元素
            display_text = text[:60] + "..." if len(text) > 60 else text
            return f"{highlight_info} {tag_name.upper()}: '{display_text}' xpath='{elem.xpath}'"

        elif tag_name in ['code', 'kbd', 'samp', 'var']:
            # 代码相关元素 - 保持原始格式
            display_text = text[:100] + "..." if len(text) > 100 else text
            return f"{highlight_info} {tag_name.upper()}: `{display_text}` xpath='{elem.xpath}'"

        elif tag_name == 'pre':
            # 预格式化文本
            lines = text.split('\n')
            if len(lines) > 3:
                preview = '\n'.join(lines[:2]) + '\n...'
            else:
                preview = text[:150] + "..." if len(text) > 150 else text
            return f"{highlight_info} PRE: ```{preview}``` xpath='{elem.xpath}'"

        elif tag_name == 'blockquote':
            # 块引用
            display_text = text[:100] + "..." if len(text) > 100 else text
            cite_attr = elem.attributes.get('cite', '')
            cite_info = f" (来源: {cite_attr})" if cite_attr else ""
            return f"{highlight_info} BLOCKQUOTE: \"{display_text}\"{cite_info} xpath='{elem.xpath}'"

        elif tag_name == 'q':
            # 行内引用
            display_text = text[:80] + "..." if len(text) > 80 else text
            return f"{highlight_info} Q: \"{display_text}\" xpath='{elem.xpath}'"

        elif tag_name == 'cite':
            # 引文来源
            return f"{highlight_info} CITE: '{text}' xpath='{elem.xpath}'"

        elif tag_name == 'time':
            # 时间元素
            datetime_attr = elem.attributes.get('datetime', '')
            datetime_info = f" ({datetime_attr})" if datetime_attr else ""
            return f"{highlight_info} TIME: '{text}'{datetime_info} xpath='{elem.xpath}'"

        elif tag_name == 'data':
            # 数据元素
            value_attr = elem.attributes.get('value', '')
            value_info = f" (值: {value_attr})" if value_attr else ""
            return f"{highlight_info} DATA: '{text}'{value_info} xpath='{elem.xpath}'"

        elif tag_name == 'abbr':
            # 缩写
            title_attr = elem.attributes.get('title', '')
            title_info = f" (全称: {title_attr})" if title_attr else ""
            return f"{highlight_info} ABBR: '{text}'{title_info} xpath='{elem.xpath}'"

        elif tag_name == 'dfn':
            # 定义术语
            return f"{highlight_info} DFN: '{text}' (定义) xpath='{elem.xpath}'"

        elif tag_name in ['div', 'span']:
            # 语义容器
            classes = elem.attributes.get('class', '')
            id_attr = elem.attributes.get('id', '')
            role = elem.attributes.get('role', '')

            # 构建标识符
            identifier_parts = []
            if classes:
                # 只显示前2个最重要的类
                class_list = classes.split()[:2]
                identifier_parts.append(f".{'.'.join(class_list)}")
            if id_attr:
                identifier_parts.append(f"#{id_attr}")
            if role:
                identifier_parts.append(f"[role={role}]")

            identifier = ''.join(identifier_parts) if identifier_parts else ''

            # 重要性评分
            importance_score = DOMFormatter._get_semantic_element_score(elem)
            importance_levels = {0: '无', 1: '低', 2: '中', 3: '高', 4: '很高', 5: '极高'}
            importance_text = importance_levels.get(min(importance_score, 5), '未知')

            display_text = text[:60] + "..." if len(text) > 60 else text
            return f"{highlight_info} {tag_name.upper()}{identifier}: '{display_text}' (重要性: {importance_text}) xpath='{elem.xpath}'"

        else:
            # 其他元素的通用格式化
            display_text = text[:60] + "..." if len(text) > 60 else text
            return f"{highlight_info} {tag_name.upper()}: '{display_text}' xpath='{elem.xpath}'"

    @staticmethod
    def format_clickable_elements(elements: List[ElementInfo]) -> str:
        """格式化可点击元素"""
        if not elements:
            return "👆 No clickable elements found on the page"

        clickable_models = [ActionableElement.from_element_info(elem) for elem in elements]

        result_lines = [f"👆 Found {len(clickable_models)} clickable elements:"]
        result_lines.extend([f"  {elem.to_line()}" for elem in clickable_models])
        return "\n".join(result_lines)

    @staticmethod
    def format_focusable_elements(elements: List[ElementInfo]) -> str:
        """格式化可聚焦元素"""
        if not elements:
            return "🎯 No focusable elements found on the page"

        result_lines = [f"🎯 Found {len(elements)} focusable elements:"]
        for elem in elements:
            text = elem.text_content[:30] + "..." if len(elem.text_content) > 30 else elem.text_content
            highlight_info = f"[{elem.highlight_index}]" if elem.highlight_index is not None else ""
            tabindex = elem.attributes.get('tabindex', '')
            tabindex_info = f" (tabindex={tabindex})" if tabindex else ""
            result_lines.append(f"  {highlight_info} {elem.tag_name}: '{text}'{tabindex_info} xpath='{elem.xpath}'")

        return "\n".join(result_lines)

    @staticmethod
    def format_interactive_elements(elements: List[ElementInfo]) -> str:
        """格式化交互元素"""
        if not elements:
            return "🔄 No interactive elements found on the page"

        actionable_models = [ActionableElement.from_element_info(elem) for elem in elements]

        result_lines = [f"🔄 Found {len(actionable_models)} interactive elements:"]
        result_lines.extend([f"  {elem.to_line()}" for elem in actionable_models])
        return "\n".join(result_lines)

    @staticmethod
    def format_generic_elements(elements: List[ElementInfo], element_type: str = "元素") -> str:
        """通用元素格式化"""
        return BaseFormatter.format_element_list(elements, element_type)
    
    @staticmethod
    def format_compact_list(elements: List[ElementInfo], max_items: int = 10) -> str:
        """紧凑格式的元素列表"""
        return BaseFormatter.format_compact_list(elements, max_items)
    
    @staticmethod
    def format_summary(elements: List[ElementInfo], element_type: str = "元素") -> str:
        """格式化元素摘要"""
        return BaseFormatter.format_summary(elements, element_type)
    
    @staticmethod
    def format_table_style(elements: List[ElementInfo], columns: List[str] = None) -> str:
        """表格样式格式化"""
        return BaseFormatter.format_table_style(elements, columns)
    
    @staticmethod
    def format_by_type(elements: List[ElementInfo], format_type: str = "default") -> str:
        """
        根据类型格式化元素
        
        Args:
            elements: 元素列表
            format_type: 格式化类型 ('default', 'compact', 'table', 'summary')
            
        Returns:
            格式化后的字符串
        """
        if not elements:
            return "未找到元素"
        
        if format_type == "compact":
            return DOMFormatter.format_compact_list(elements)
        elif format_type == "table":
            return DOMFormatter.format_table_style(elements)
        elif format_type == "summary":
            return DOMFormatter.format_summary(elements)
        else:
            return DOMFormatter.format_generic_elements(elements)
    
    @staticmethod
    def format_mixed_elements(elements: List[ElementInfo]) -> str:
        """
        格式化混合类型的元素列表
        
        Args:
            elements: 混合类型的元素列表
            
        Returns:
            按类型分组格式化的字符串
        """
        if not elements:
            return "未找到元素"
        
        # 按标签类型分组
        element_groups = {}
        for elem in elements:
            tag_name = elem.tag_name.lower()
            if tag_name not in element_groups:
                element_groups[tag_name] = []
            element_groups[tag_name].append(elem)
        
        result_lines = [f"找到 {len(elements)} 个元素，按类型分组:"]
        result_lines.append("")
        
        # 按类型格式化
        for tag_name, group_elements in sorted(element_groups.items()):
            result_lines.append(f"=== {tag_name.upper()} 元素 ({len(group_elements)}个) ===")
            
            if tag_name == 'a':
                result_lines.append(DOMFormatter.format_links(group_elements))
            elif tag_name == 'button':
                result_lines.append(DOMFormatter.format_buttons(group_elements))
            elif tag_name == 'input':
                result_lines.append(DOMFormatter.format_inputs(group_elements))
            else:
                result_lines.append(DOMFormatter.format_generic_elements(group_elements, tag_name))
            
            result_lines.append("")
        
        return "\n".join(result_lines)
