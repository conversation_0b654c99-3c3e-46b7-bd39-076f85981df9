"""
iICrawlerMCP - A LangChain-based web crawler with Playwright integration.

This package provides tools for web crawling using LangChain agents and Playwright browser automation.
"""

__version__ = "0.1.0"
__author__ = "iICrawlerMCP Team"

# Core components
from .core import Browser, get_global_browser, close_global_browser, config

# DOM extraction
from .dom import DOMExtractor, ElementInfo

# Agents
from .agents import (
    CrawlerAgent, build_agent, create_simple_agent,
    BrowserAgent, build_browser_agent,
    ElementAgent, build_element_agent
)

# Tools
from .tools import (
    BrowserToolkit, get_tools, get_browser_specific_tools, cleanup_tools,
    # Basic browser tools (legacy compatibility)
    navigate_browser as navigate, take_screenshot as screenshot, get_page_info,
    click_element as browser_click, type_text as browser_type, hover_element,
    # Advanced browser tools
    browser_snapshot, browser_wait_for, browser_evaluate, browser_hover,
    browser_press_key, browser_select_option
)

# MCP Server (optional)
try:
    from .mcp import MC<PERSON>erver, start_mcp_server, intelligent_web_task
    _MCP_AVAILABLE = True
except ImportError:
    _MCP_AVAILABLE = False
    MCPServer = None
    start_mcp_server = None
    intelligent_web_task = None

__all__ = [
    # Core
    "Browser",
    "get_global_browser",
    "close_global_browser",
    "config",

    # DOM
    "DOMExtractor",
    "ElementInfo",

    # Agents
    "CrawlerAgent",
    "build_agent",
    "create_simple_agent",
    "BrowserAgent",
    "build_browser_agent",
    "ElementAgent",
    "build_element_agent",

    # Tools
    "BrowserToolkit",
    "get_tools",
    "get_browser_specific_tools",
    "cleanup_tools",
    # Legacy compatibility
    "navigate",
    "screenshot",
    "get_page_info",
    "browser_snapshot",
    "browser_click",
    "browser_type",
    "browser_wait_for",
    "browser_evaluate",
    "browser_hover",
    "browser_press_key",
    "browser_select_option",

    # MCP (optional)
    "MCPServer",
    "start_mcp_server",
    "intelligent_web_task",
    "_MCP_AVAILABLE",
]
