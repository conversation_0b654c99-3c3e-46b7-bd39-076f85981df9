#!/usr/bin/env python3
"""
Basic Agent Usage Example

This example demonstrates the basic usage of iICrawlerMCP agents.
Shows how to use both CrawlerAgent and BrowserAgent.
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent, build_browser_agent
from iicrawlermcp.core.config import config

def basic_crawler_agent_example():
    """Demonstrate basic CrawlerAgent usage."""
    print("\n🤖 CrawlerAgent Basic Example")
    print("-" * 30)
    
    try:
        # Create crawler agent
        print("📱 Creating CrawlerAgent...")
        crawler_agent = build_agent()
        
        # Simple task
        task = "Navigate to https://httpbin.org/html and get the page title"
        print(f"🎯 Task: {task}")
        
        result = crawler_agent.invoke(task)
        print(f"✅ Result: {result['output']}")
        
        # Clean up
        crawler_agent.cleanup()
        
    except Exception as e:
        print(f"❌ CrawlerAgent error: {e}")

def basic_browser_agent_example():
    """Demonstrate basic BrowserAgent usage."""
    print("\n🌐 BrowserAgent Basic Example")
    print("-" * 30)
    
    try:
        # Create browser agent
        print("📱 Creating BrowserAgent...")
        browser_agent = build_browser_agent()
        
        # Direct method calls
        print("🔗 Direct navigation...")
        browser_agent.navigate_to("https://httpbin.org/html")
        
        print("📄 Getting page info...")
        page_info = browser_agent.get_page_info()
        print(f"  Title: {page_info['title']}")
        print(f"  URL: {page_info['url']}")
        
        print("📸 Taking screenshot...")
        screenshot_path = browser_agent.take_screenshot()
        print(f"  Screenshot: {screenshot_path}")
        
        # Clean up
        browser_agent.cleanup()
        
    except Exception as e:
        print(f"❌ BrowserAgent error: {e}")

def agent_collaboration_example():
    """Demonstrate agent collaboration."""
    print("\n🤝 Agent Collaboration Example")
    print("-" * 30)
    
    try:
        # Create main agent
        print("📱 Creating CrawlerAgent...")
        crawler_agent = build_agent()
        
        # Delegate browser task
        browser_task = "Navigate to https://httpbin.org/html and take a screenshot"
        print(f"🎯 Delegating task: {browser_task}")
        
        result = crawler_agent.delegate_browser_task(browser_task)
        print(f"✅ Delegation result: {result['output']}")
        
        # Clean up
        crawler_agent.cleanup()
        
    except Exception as e:
        print(f"❌ Collaboration error: {e}")

def main():
    """Main function."""
    print("🎯 iICrawlerMCP Basic Agent Examples")
    print("This example shows basic agent usage patterns.\n")
    
    try:
        # Validate configuration
        config.validate()
        print("✅ Configuration validated")
        
        # Run examples
        basic_crawler_agent_example()
        basic_browser_agent_example()
        agent_collaboration_example()
        
        print("\n🎉 All examples completed!")
        print("💡 Next: Try examples/advanced/ for more complex scenarios")
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        print("💡 Please check your .env file and API keys")

if __name__ == "__main__":
    main()
