# Examples Directory

This directory contains examples and demonstrations of iICrawlerMCP usage.

## Structure

- `basic/` - Simple examples for getting started
- `advanced/` - Complex examples showing advanced features

## Basic Examples

Start here if you're new to iICrawlerMCP:

- `browser_basics.py` - Basic browser automation
- `agent_basics.py` - Simple agent usage
- `browser_visible.py` - Visual browser demonstration
- `element_agent_basics.py` - Basic element interaction
- `qqeng_simple_visit.py` - Simple English learning website visit

## Advanced Examples

For more complex use cases:

- `browser_agent_demo.py` - BrowserAgent demonstration
- `multi_agent_collaboration.py` - Multi-agent coordination
- `wikipedia_disney_research.py` - Wikipedia information research
- `qqeng_learning_research.py` - English learning website research
- `disney_ticket_research.py` - Disney ticket information research
- `intelligent_element_features.py` - Advanced element interaction features

## Running Examples

```bash
# Run from project root
python examples/basic/browser_basics.py
python examples/advanced/browser_agent_demo.py
```

## Screenshots

Example screenshots are saved to `screenshots/examples/`
