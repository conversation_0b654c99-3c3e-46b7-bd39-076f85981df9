"""
Element Agent module for iICrawlerMCP.

This module provides a specialized agent for DOM element analysis and manipulation.
The ElementAgent focuses specifically on intelligent element discovery, analysis,
and precise element operations.
"""

import logging
from typing import Optional, List, Dict, Any
from langchain import hub
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import Chat<PERSON>penAI
from langchain_core.tools import BaseTool

from ..core.config import config
from ..core.browser import get_global_browser
from ..dom.core.extractor import DOMExtractor
from ..dom.models.base_models import ElementInfo

logger = logging.getLogger(__name__)


class ElementAgent:
    """
    A specialized agent for DOM element analysis and manipulation.
    
    This agent focuses on element-specific operations including:
    - Intelligent element discovery and search
    - Element state analysis and validation
    - Precise element interaction operations
    - Form automation and processing
    - Element relationship analysis
    """
    
    def __init__(
        self, 
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None
    ):
        """
        Initialize the ElementAgent.
        
        Args:
            tools: List of LangChain tools to use. If None, uses DOM-specific tools.
            verbose: Whether to enable verbose logging. If None, uses config default.
            llm_config: Custom LLM configuration. If None, uses config defaults.
        """
        self.tools = tools or self._get_element_tools()
        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()
        
        self._llm = None
        self._agent = None
        self._executor = None
        self._browser = None
        self._dom_extractor = None
    
    def _get_element_tools(self) -> List[BaseTool]:
        """Get element-specific tools."""
        from ..tools.dom_tools_enhanced import get_enhanced_dom_tools
        from ..tools.browser_tools import (
            click_element, type_text, hover_element
        )

        # Get enhanced DOM analysis tools only
        element_tools = get_enhanced_dom_tools()

        # Add essential browser interaction tools
        element_tools.extend([
            click_element,
            type_text,
            hover_element
        ])

        return element_tools
    
    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"ElementAgent LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create ElementAgent LLM: {e}")
                raise
        return self._llm
    
    def _create_agent(self) -> None:
        """Create the LangChain agent with element-specific prompt."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                # Use element-specific prompt or the default one
                prompt = hub.pull("hwchase17/openai-functions-agent")
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("ElementAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create ElementAgent: {e}")
                raise
    
    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=50,  # 增加最大迭代次数，支持复杂任务
                    handle_parsing_errors=True
                )
                logger.info("ElementAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create ElementAgent executor: {e}")
                raise
        return self._executor
    
    def get_browser(self):
        """Get the browser instance managed by this agent."""
        if self._browser is None:
            self._browser = get_global_browser()
        return self._browser
    
    def get_dom_extractor(self) -> DOMExtractor:
        """Get the DOM extractor instance."""
        if self._dom_extractor is None:
            browser = self.get_browser()
            # Ensure browser is initialized
            if not browser._is_initialized:
                browser._initialize()
            self._dom_extractor = DOMExtractor(browser)
        return self._dom_extractor
    
    def find_element_by_description(self, description: str, element_type: Optional[str] = None) -> Optional[ElementInfo]:
        """
        Find the best matching element based on human description.
        
        Args:
            description: Human-readable description of the element
            element_type: Optional element type filter
            
        Returns:
            ElementInfo object if found, None otherwise
        """
        try:
            extractor = self.get_dom_extractor()
            element = extractor.find_best_match_element(description, element_type)
            if element:
                logger.info(f"ElementAgent found element for '{description}': {element.id}")
            else:
                logger.warning(f"ElementAgent could not find element for '{description}'")
            return element
        except Exception as e:
            logger.error(f"ElementAgent find_element_by_description failed: {e}")
            return None
    
    def analyze_page_structure(self) -> Dict[str, Any]:
        """
        Analyze the current page's DOM structure.
        
        Returns:
            Dictionary containing page structure analysis
        """
        try:
            extractor = self.get_dom_extractor()
            summary = extractor.get_elements_summary()
            
            # Add additional analysis
            interactive_elements = extractor.get_interactive_elements()
            form_elements = extractor.get_form_elements()
            
            analysis = {
                'summary': summary,
                'interactive_count': len(interactive_elements),
                'form_count': len(form_elements),
                'has_forms': len(form_elements) > 0,
                'has_navigation': len(extractor.get_navigation_elements()) > 0
            }
            
            logger.info("ElementAgent completed page structure analysis")
            return analysis
        except Exception as e:
            logger.error(f"ElementAgent analyze_page_structure failed: {e}")
            return {}
    
    def verify_element_state(self, element_id: str) -> Dict[str, bool]:
        """
        Verify the current state of an element.
        
        Args:
            element_id: The element ID to verify
            
        Returns:
            Dictionary containing element state information
        """
        try:
            extractor = self.get_dom_extractor()
            element = extractor.get_element_by_id(element_id)
            
            if not element:
                return {'exists': False}
            
            state = {
                'exists': True,
                'is_visible': element.is_visible,
                'is_interactive': element.is_interactive,
                'is_in_viewport': element.is_in_viewport,
                'has_text': bool(element.text_content.strip()),
                'has_highlight': element.highlight_index is not None
            }
            
            logger.info(f"ElementAgent verified state for element {element_id}")
            return state
        except Exception as e:
            logger.error(f"ElementAgent verify_element_state failed: {e}")
            return {'exists': False, 'error': str(e)}
    
    def get_element_context(self, element_id: str) -> Dict[str, Any]:
        """
        Get contextual information about an element including its relationships.

        Args:
            element_id: The element ID to analyze

        Returns:
            Dictionary containing element context information
        """
        try:
            extractor = self.get_dom_extractor()
            element = extractor.get_element_by_id(element_id)

            if not element:
                return {'error': 'Element not found'}

            children = extractor.get_element_children(element_id)
            parent = extractor.get_element_parent(element_id)

            context = {
                'element': {
                    'id': element.id,
                    'tag_name': element.tag_name,
                    'text_content': element.text_content,
                    'xpath': element.xpath
                },
                'parent': {
                    'id': parent.id,
                    'tag_name': parent.tag_name,
                    'text_content': parent.text_content[:50]
                } if parent else None,
                'children_count': len(children),
                'children': [
                    {
                        'id': child.id,
                        'tag_name': child.tag_name,
                        'text_content': child.text_content[:30]
                    } for child in children[:5]  # Limit to first 5 children
                ]
            }

            logger.info(f"ElementAgent retrieved context for element {element_id}")
            return context
        except Exception as e:
            logger.error(f"ElementAgent get_element_context failed: {e}")
            return {'error': str(e)}

    def intelligent_form_analysis(self) -> Dict[str, Any]:
        """
        Perform intelligent analysis of forms on the current page.

        Returns:
            Dictionary containing intelligent form analysis results
        """
        try:
            extractor = self.get_dom_extractor()
            form_elements = extractor.get_form_elements()

            if not form_elements:
                return {'forms': [], 'analysis': 'No forms found on the page'}

            # Group elements by form
            forms = {}
            for elem in form_elements:
                if elem.tag_name.lower() == 'form':
                    forms[elem.id] = {
                        'form_element': elem,
                        'inputs': [],
                        'buttons': [],
                        'other_elements': []
                    }

            # If no form tags found, create a default group
            if not forms:
                forms['default'] = {
                    'form_element': None,
                    'inputs': [],
                    'buttons': [],
                    'other_elements': []
                }

            # Categorize form elements
            for elem in form_elements:
                if elem.tag_name.lower() == 'form':
                    continue

                # Find the parent form or use default
                parent_form_id = 'default'
                for form_id, form_data in forms.items():
                    if form_data['form_element'] and form_id in elem.xpath:
                        parent_form_id = form_id
                        break

                if elem.tag_name.lower() in ['input', 'textarea', 'select']:
                    forms[parent_form_id]['inputs'].append(elem)
                elif elem.tag_name.lower() == 'button':
                    forms[parent_form_id]['buttons'].append(elem)
                else:
                    forms[parent_form_id]['other_elements'].append(elem)

            # Analyze each form
            analysis_results = []
            for form_id, form_data in forms.items():
                form_analysis = {
                    'form_id': form_id,
                    'input_count': len(form_data['inputs']),
                    'button_count': len(form_data['buttons']),
                    'field_types': {},
                    'required_fields': 0,
                    'accessibility_score': 0,
                    'suggestions': []
                }

                # Analyze input types
                for inp in form_data['inputs']:
                    input_type = inp.attributes.get('type', 'text')
                    form_analysis['field_types'][input_type] = form_analysis['field_types'].get(input_type, 0) + 1

                    if inp.attributes.get('required'):
                        form_analysis['required_fields'] += 1

                # Calculate accessibility score
                labeled_inputs = 0
                for inp in form_data['inputs']:
                    if (inp.attributes.get('aria-label') or
                        inp.attributes.get('aria-labelledby') or
                        inp.attributes.get('placeholder')):
                        labeled_inputs += 1

                if form_data['inputs']:
                    form_analysis['accessibility_score'] = int((labeled_inputs / len(form_data['inputs'])) * 100)

                # Generate suggestions
                if form_analysis['accessibility_score'] < 80:
                    form_analysis['suggestions'].append("Consider adding labels or aria-labels to form inputs")

                if not form_data['buttons']:
                    form_analysis['suggestions'].append("Form appears to be missing a submit button")

                analysis_results.append(form_analysis)

            result = {
                'forms': analysis_results,
                'total_forms': len(forms),
                'total_inputs': sum(len(form_data['inputs']) for form_data in forms.values()),
                'analysis': f"Found {len(forms)} forms with {sum(len(form_data['inputs']) for form_data in forms.values())} total input fields"
            }

            logger.info(f"ElementAgent completed intelligent form analysis: {len(forms)} forms")
            return result

        except Exception as e:
            logger.error(f"ElementAgent intelligent_form_analysis failed: {e}")
            return {'error': str(e)}

    def suggest_automation_strategy(self, task_description: str) -> Dict[str, Any]:
        """
        Suggest an automation strategy for a given task based on page analysis.

        Args:
            task_description: Description of the automation task

        Returns:
            Dictionary containing suggested automation strategy
        """
        try:
            extractor = self.get_dom_extractor()

            # Analyze page structure
            interactive_elements = extractor.get_interactive_elements()
            form_elements = extractor.get_form_elements()

            strategy = {
                'task': task_description,
                'recommended_approach': [],
                'required_elements': [],
                'potential_challenges': [],
                'confidence_score': 0
            }

            task_lower = task_description.lower()

            # Analyze task type and suggest approach
            if any(keyword in task_lower for keyword in ['fill', 'form', 'submit', 'enter']):
                if form_elements:
                    strategy['recommended_approach'].append("Use form automation approach")
                    strategy['required_elements'].extend([
                        f"Form element: {elem.tag_name}[{elem.highlight_index}]"
                        for elem in form_elements[:5] if elem.highlight_index is not None
                    ])
                    strategy['confidence_score'] += 30
                else:
                    strategy['potential_challenges'].append("No form elements found for form filling task")

            if any(keyword in task_lower for keyword in ['click', 'button', 'link']):
                clickable_elements = [elem for elem in interactive_elements if elem.is_interactive]
                if clickable_elements:
                    strategy['recommended_approach'].append("Use element clicking approach")
                    strategy['required_elements'].extend([
                        f"Clickable element: {elem.tag_name}[{elem.highlight_index}]"
                        for elem in clickable_elements[:3] if elem.highlight_index is not None
                    ])
                    strategy['confidence_score'] += 25
                else:
                    strategy['potential_challenges'].append("Limited clickable elements found")

            if any(keyword in task_lower for keyword in ['navigate', 'go to', 'visit']):
                links = [elem for elem in interactive_elements if elem.tag_name.lower() == 'a']
                if links:
                    strategy['recommended_approach'].append("Use navigation link approach")
                    strategy['required_elements'].extend([
                        f"Navigation link: {elem.text_content[:30]}[{elem.highlight_index}]"
                        for elem in links[:3] if elem.highlight_index is not None
                    ])
                    strategy['confidence_score'] += 20

            if any(keyword in task_lower for keyword in ['extract', 'scrape', 'get data']):
                strategy['recommended_approach'].append("Use data extraction approach")
                strategy['confidence_score'] += 15

            # General recommendations
            if len(interactive_elements) > 20:
                strategy['potential_challenges'].append("Page has many interactive elements - may need precise targeting")

            if strategy['confidence_score'] < 30:
                strategy['potential_challenges'].append("Task description may need more specific details")

            if not strategy['recommended_approach']:
                strategy['recommended_approach'].append("Manual element analysis required")

            logger.info(f"ElementAgent generated automation strategy with confidence {strategy['confidence_score']}")
            return strategy

        except Exception as e:
            logger.error(f"ElementAgent suggest_automation_strategy failed: {e}")
            return {'error': str(e)}
    
    def invoke(self, input_text: str) -> dict:
        """
        Execute an element analysis or manipulation task using the agent.
        
        Args:
            input_text: The task description or instruction for the element agent.
            
        Returns:
            A dictionary containing the agent's response and output.
        """
        executor = self._create_executor()
        
        try:
            logger.info(f"ElementAgent executing task: {input_text}")
            result = executor.invoke({"input": input_text})
            logger.info("ElementAgent task completed successfully")
            return result
        except Exception as e:
            logger.error(f"ElementAgent task execution failed: {e}")
            raise
    
    def cleanup(self) -> None:
        """Clean up resources used by the element agent."""
        try:
            if self._browser:
                # Don't close the global browser, just clear reference
                self._browser = None
            if self._dom_extractor:
                self._dom_extractor = None
            logger.info("ElementAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during ElementAgent cleanup: {e}")


def build_element_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None
) -> ElementAgent:
    """
    Build and return a configured ElementAgent instance.
    
    Args:
        tools: List of LangChain tools to use. If None, uses element-specific tools.
        verbose: Whether to enable verbose logging. If None, uses config default.
        llm_config: Custom LLM configuration. If None, uses config defaults.
        
    Returns:
        A configured ElementAgent instance.
        
    Example:
        element_agent = build_element_agent()
        result = element_agent.invoke("Find all login buttons on the page")
    """
    # Validate configuration before creating agent
    config.validate()
    
    try:
        agent = ElementAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("ElementAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build ElementAgent: {e}")
        raise
