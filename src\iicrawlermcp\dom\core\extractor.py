"""
Core DOM extraction functionality.

This module contains the simplified DOM extractor that focuses on core extraction logic.
"""

import os
import logging
from typing import Dict, Any, List, Optional
from ..models.base_models import ElementInfo
from .cache_manager import CacheManager
from ...core.config import config

logger = logging.getLogger(__name__)


class DOMExtractor:
    """
    DOM element extraction and analysis engine.
    
    This class provides methods to extract, filter, and query DOM elements
    using the JavaScript DOM extraction engine.
    """
    
    def __init__(self, browser_instance=None):
        """
        Initialize the DOM extractor.
        
        Args:
            browser_instance: Browser instance to use for DOM extraction
        """
        self.browser = browser_instance
        self._dom_js_path = os.path.join(os.path.dirname(__file__), '..', 'index.js')
        self.cache_manager = CacheManager()
    
    def _load_dom_js(self) -> str:
        """Load the DOM extraction JavaScript code."""
        try:
            with open(self._dom_js_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            raise Exception(f"DOM extraction JavaScript not found at {self._dom_js_path}")
    
    def _execute_dom_extraction(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the DOM extraction JavaScript with given arguments.
        
        Args:
            args: Arguments to pass to the DOM extraction script
            
        Returns:
            Dictionary containing the DOM extraction results
        """
        if not self.browser:
            raise Exception("Browser instance not available")
        
        try:
            js_code = self._load_dom_js()
            result = self.browser._page.evaluate(js_code, args)
            self.cache_manager.set_last_extraction_result(result)
            return result
        except Exception as e:
            logger.error(f"DOM extraction failed: {e}")
            raise Exception(f"DOM extraction failed: {e}")
    
    def _parse_element_data(self, element_id: str, element_data: Dict[str, Any], 
                           element_map: Dict[str, Any]) -> ElementInfo:
        """
        Parse raw element data into ElementInfo object.
        
        Args:
            element_id: The element ID
            element_data: Raw element data from DOM extraction
            element_map: Complete element map for reference
            
        Returns:
            ElementInfo object
        """
        # Skip text nodes
        if element_data.get('type') == 'TEXT_NODE':
            return None
        
        # Extract text content from children
        text_content = self._extract_text_content(element_data, element_map)
        
        return ElementInfo(
            id=element_id,
            tag_name=element_data.get('tagName', ''),
            xpath=element_data.get('xpath', ''),
            attributes=element_data.get('attributes', {}),
            text_content=text_content,
            highlight_index=element_data.get('highlightIndex'),
            is_visible=element_data.get('isVisible', False),
            is_interactive=element_data.get('isInteractive', False),
            is_in_viewport=element_data.get('isInViewport', False),
            children_ids=element_data.get('children', [])
        )
    
    def _extract_text_content(self, element_data: Dict[str, Any], 
                             element_map: Dict[str, Any]) -> str:
        """
        Extract text content from element and its children.
        
        Args:
            element_data: Element data dictionary
            element_map: Complete element map for reference
            
        Returns:
            Extracted text content
        """
        text_parts = []
        
        # Add direct text content
        if element_data.get('textContent'):
            text_parts.append(element_data['textContent'].strip())
        
        # Add text from children (recursively)
        for child_id in element_data.get('children', []):
            if child_id in element_map:
                child_data = element_map[child_id]
                if child_data.get('type') == 'TEXT_NODE':
                    # Fix: JavaScript stores TEXT_NODE content in 'text' field, not 'textContent'
                    child_text = child_data.get('text', '').strip()
                    if child_text:
                        text_parts.append(child_text)
                else:
                    child_text = self._extract_text_content(child_data, element_map)
                    if child_text:
                        text_parts.append(child_text)
        
        return ' '.join(text_parts).strip()
    
    def _process_extraction_result(self, result: Dict[str, Any]) -> List[ElementInfo]:
        """
        Process DOM extraction result and convert to ElementInfo objects.

        Args:
            result: Raw DOM extraction result

        Returns:
            List of ElementInfo objects
        """
        if not result:
            return []

        # Handle the format returned by index.js: { rootId, map: DOM_HASH_MAP }
        element_map = result.get('map', {})
        if not element_map:
            return []

        elements = []

        for element_id, element_data in element_map.items():
            element_info = self._parse_element_data(element_id, element_data, element_map)
            if element_info:
                elements.append(element_info)

        return elements
    
    def extract_all_elements(self, **kwargs) -> List[ElementInfo]:
        """
        Extract all DOM elements from the current page.
        
        Args:
            **kwargs: Additional arguments for DOM extraction
            
        Returns:
            List of ElementInfo objects
        """
        # Check cache first
        cache_key = f"all_elements_{hash(str(kwargs))}"
        cached_result = self.cache_manager.get_cached_elements(cache_key)
        if cached_result:
            return [self.cache_manager._dict_to_element(elem_data) 
                   for elem_data in cached_result.get('elements', [])]
        
        # Execute DOM extraction with full configuration
        highlight_config = config.get_highlight_config()
        extraction_config = config.get_dom_extraction_config()

        args = {
            'doHighlightElements': kwargs.get('highlight_elements', highlight_config.get('doHighlightElements', True)),
            'focusHighlightIndex': kwargs.get('focus_highlight_index', extraction_config.get('focusHighlightIndex', -1)),
            'viewportExpansion': kwargs.get('viewport_expansion', extraction_config.get('viewportExpansion', 0)),
            'debugMode': kwargs.get('debug_mode', extraction_config.get('debugMode', False)),
            'highlightConfig': highlight_config,
            'extractionConfig': extraction_config,
            **kwargs
        }
        
        result = self._execute_dom_extraction(args)
        elements = self._process_extraction_result(result)
        
        # Cache the result
        self.cache_manager.cache_elements(cache_key, elements)
        
        return elements
    
    def find_elements_by_text(self, text: str, exact_match: bool = False, case_sensitive: bool = True) -> List[ElementInfo]:
        """
        Find elements containing specific text.
        
        Args:
            text: Text to search for
            exact_match: Whether to perform exact text matching
            
        Returns:
            List of matching ElementInfo objects
        """
        # Check cache first
        cache_key = f"text_search_{text}_{exact_match}"
        cached_result = self.cache_manager.get_cached_elements(cache_key)
        if cached_result:
            return [self.cache_manager._dict_to_element(elem_data) 
                   for elem_data in cached_result.get('elements', [])]
        
        # Execute DOM extraction with text search and full configuration
        highlight_config = config.get_highlight_config()
        extraction_config = config.get_dom_extraction_config()

        args = {
            'doHighlightElements': highlight_config.get('doHighlightElements', True),
            'focusHighlightIndex': extraction_config.get('focusHighlightIndex', -1),
            'viewportExpansion': extraction_config.get('viewportExpansion', 0),
            'debugMode': extraction_config.get('debugMode', False),
            'highlightConfig': highlight_config,
            'extractionConfig': extraction_config,
            'searchText': text,
            'exactMatch': exact_match
        }
        
        result = self._execute_dom_extraction(args)
        elements = self._process_extraction_result(result)
        
        # Cache the result
        self.cache_manager.cache_elements(cache_key, elements)
        
        return elements
    
    def get_interactive_elements(self) -> List[ElementInfo]:
        """
        Get all interactive elements from the current page.
        
        Returns:
            List of interactive ElementInfo objects
        """
        # Check cache first
        cache_key = "interactive_elements"
        cached_result = self.cache_manager.get_cached_elements(cache_key)
        if cached_result:
            return [self.cache_manager._dict_to_element(elem_data) 
                   for elem_data in cached_result.get('elements', [])]
        
        # Execute DOM extraction for interactive elements with full configuration
        highlight_config = config.get_highlight_config()
        extraction_config = config.get_dom_extraction_config()

        args = {
            'doHighlightElements': highlight_config.get('doHighlightElements', True),
            'focusHighlightIndex': extraction_config.get('focusHighlightIndex', -1),
            'viewportExpansion': extraction_config.get('viewportExpansion', 0),
            'debugMode': extraction_config.get('debugMode', False),
            'highlightConfig': highlight_config,
            'extractionConfig': extraction_config
        }
        
        result = self._execute_dom_extraction(args)
        elements = self._process_extraction_result(result)
        
        # Cache the result
        self.cache_manager.cache_elements(cache_key, elements)
        
        return elements
    
    def clear_cache(self) -> None:
        """Clear all cached data."""
        self.cache_manager.clear_cache()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return self.cache_manager.get_cache_stats()

    def get_clickable_elements(self) -> List[ElementInfo]:
        """
        Get all clickable elements from the current page.

        Returns:
            List of clickable ElementInfo objects
        """
        # Get all interactive elements and filter for clickable ones
        interactive_elements = self.get_interactive_elements()
        clickable_tags = {'button', 'a', 'input'}

        clickable_elements = []
        for elem in interactive_elements:
            if (elem.tag_name.lower() in clickable_tags or
                elem.attributes.get('onclick') or
                elem.attributes.get('role') == 'button'):
                clickable_elements.append(elem)

        return clickable_elements

    def get_input_elements(self) -> List[ElementInfo]:
        """
        Get all input elements from the current page.

        Returns:
            List of input ElementInfo objects
        """
        all_elements = self.extract_all_elements(highlight_elements=False)
        input_elements = []

        for elem in all_elements:
            if (elem.tag_name.lower() in {'input', 'textarea', 'select'} or
                elem.attributes.get('contenteditable') == 'true'):
                input_elements.append(elem)

        return input_elements

    def find_buttons_with_text(self, text: str) -> List[ElementInfo]:
        """
        Find buttons containing specific text.

        Args:
            text: Text to search for in buttons

        Returns:
            List of button ElementInfo objects containing the text
        """
        all_elements = self.extract_all_elements(highlight_elements=False)
        button_elements = []

        text_lower = text.lower()
        for elem in all_elements:
            if elem.tag_name.lower() == 'button' or elem.attributes.get('type') == 'button':
                if text_lower in elem.text_content.lower():
                    button_elements.append(elem)

        return button_elements

    def get_elements_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all elements on the page.

        Returns:
            Dictionary containing element statistics
        """
        all_elements = self.extract_all_elements(highlight_elements=False)

        summary = {
            'total_elements': len(all_elements),
            'visible_elements': sum(1 for elem in all_elements if elem.is_visible),
            'interactive_elements': sum(1 for elem in all_elements if elem.is_interactive),
            'elements_by_tag': {}
        }

        # Count elements by tag
        for elem in all_elements:
            tag = elem.tag_name.lower()
            summary['elements_by_tag'][tag] = summary['elements_by_tag'].get(tag, 0) + 1

        return summary
