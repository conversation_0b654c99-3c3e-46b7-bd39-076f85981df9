#!/usr/bin/env python3
"""
iICrawlerMCP客户端演示脚本

演示如何使用MCP客户端连接到iICrawlerMCP服务器并执行各种网页任务。

使用方法:
    1. 先启动MCP服务器: python scripts/start_mcp_server.py
    2. 在另一个终端运行: python scripts/mcp_client_demo.py
"""

import asyncio
import sys
import subprocess
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from mcp import ClientSession
    from mcp.client.stdio import stdio_client
except ImportError:
    print("❌ 缺少MCP客户端依赖，请安装: pip install mcp", file=sys.stderr)
    sys.exit(1)


class MCPClientDemo:
    """MCP客户端演示类"""
    
    def __init__(self):
        self.server_process = None
        self.session = None
    
    async def start_server(self):
        """启动MCP服务器"""
        print("🚀 启动MCP服务器...")
        
        # 启动服务器进程
        server_script = project_root / "scripts" / "start_mcp_server.py"
        self.server_process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # 等待服务器启动
        await asyncio.sleep(2)
        
        if self.server_process.poll() is not None:
            # 服务器启动失败
            stderr = self.server_process.stderr.read().decode()
            raise RuntimeError(f"服务器启动失败: {stderr}")
        
        print("✅ MCP服务器启动成功")
    
    async def connect_client(self):
        """连接MCP客户端"""
        print("🔌 连接MCP客户端...")
        
        # 创建stdio客户端连接到服务器
        read_stream = self.server_process.stdout
        write_stream = self.server_process.stdin
        
        self.session = ClientSession(read_stream, write_stream)
        await self.session.initialize()
        
        print("✅ MCP客户端连接成功")
    
    async def demo_list_tools(self):
        """演示：列出可用工具"""
        print("\n📋 演示1: 列出可用工具")
        print("-" * 30)
        
        tools = await self.session.list_tools()
        print(f"可用工具数量: {len(tools.tools)}")
        
        for tool in tools.tools:
            print(f"🔧 {tool.name}: {tool.description}")
    
    async def demo_browser_status(self):
        """演示：获取浏览器状态"""
        print("\n🌐 演示2: 获取浏览器状态")
        print("-" * 30)
        
        result = await self.session.call_tool("browser_status", {})
        print(f"浏览器状态: {result.content[0].text}")
    
    async def demo_simple_task(self):
        """演示：执行简单任务"""
        print("\n🎯 演示3: 执行简单网页任务")
        print("-" * 30)
        
        task = "打开google.com并截图"
        print(f"任务: {task}")
        
        result = await self.session.call_tool(
            "intelligent_web_task",
            {"task_description": task}
        )
        
        print(f"执行结果: {result.content[0].text}")
    
    async def demo_screenshot(self):
        """演示：快速截图"""
        print("\n📸 演示4: 快速截图")
        print("-" * 30)
        
        result = await self.session.call_tool(
            "take_screenshot",
            {"filename": "demo_screenshot.png"}
        )
        
        print(f"截图结果: {result.content[0].text}")
    
    async def demo_complex_task(self):
        """演示：执行复杂任务"""
        print("\n🚀 演示5: 执行复杂网页任务")
        print("-" * 30)
        
        task = "打开httpbin.org/forms/post，找到表单，填写一些测试数据，然后截图"
        print(f"任务: {task}")
        
        result = await self.session.call_tool(
            "intelligent_web_task", 
            {"task_description": task}
        )
        
        print(f"执行结果: {result.content[0].text}")
    
    async def demo_cleanup(self):
        """演示：清理资源"""
        print("\n🧹 演示6: 清理浏览器资源")
        print("-" * 30)
        
        result = await self.session.call_tool("cleanup_browser", {})
        print(f"清理结果: {result.content[0].text}")
    
    async def cleanup(self):
        """清理资源"""
        if self.session:
            # 这里可以添加session清理逻辑
            pass
        
        if self.server_process:
            print("🛑 停止MCP服务器...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            print("✅ MCP服务器已停止")
    
    async def run_all_demos(self):
        """运行所有演示"""
        try:
            await self.start_server()
            await self.connect_client()
            
            # 运行各种演示
            await self.demo_list_tools()
            await self.demo_browser_status()
            await self.demo_simple_task()
            await self.demo_screenshot()
            await self.demo_complex_task()
            await self.demo_cleanup()
            
            print("\n🎉 所有演示完成!")
            
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            raise
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    print("🎭 iICrawlerMCP客户端演示")
    print("=" * 50)
    
    # 检查环境
    import os
    if not os.getenv("OPENAI_API_KEY"):
        print("❌ 请设置OPENAI_API_KEY环境变量")
        sys.exit(1)
    
    demo = MCPClientDemo()
    
    try:
        await demo.run_all_demos()
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
        await demo.cleanup()
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        await demo.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
