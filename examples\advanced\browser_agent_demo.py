#!/usr/bin/env python3
"""
BrowserAgent Demo for iICrawlerMCP.

This script demonstrates the usage of the specialized BrowserAgent
for browser automation tasks.
"""

import logging
import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_browser_agent
from iicrawlermcp.core import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def demo_basic_browser_operations():
    """Demonstrate basic browser operations using BrowserAgent."""
    print("\n" + "="*60)
    print("🌐 BrowserAgent Basic Operations Demo")
    print("="*60)
    
    try:
        # Build the browser agent
        logger.info("Building BrowserAgent...")
        browser_agent = build_browser_agent()
        
        # Test 1: Navigate to a website
        print("\n📍 Test 1: Navigation")
        result = browser_agent.invoke("Navigate to https://httpbin.org/html")
        print(f"Result: {result['output']}")
        
        # Test 2: Take a screenshot
        print("\n📸 Test 2: Screenshot")
        result = browser_agent.invoke("Take a screenshot of the current page")
        print(f"Result: {result['output']}")
        
        # Test 3: Get page information
        print("\n📄 Test 3: Page Information")
        result = browser_agent.invoke("Get the current page title and URL")
        print(f"Result: {result['output']}")
        
        # Clean up
        browser_agent.cleanup()
        print("\n✅ BrowserAgent demo completed successfully!")
        
    except Exception as e:
        logger.error(f"BrowserAgent demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")


def test_direct_browser_methods():
    """Demonstrate direct method calls on BrowserAgent."""
    print("\n" + "="*60)
    print("🔧 BrowserAgent Direct Methods Demo")
    print("="*60)
    
    try:
        # Build the browser agent
        browser_agent = build_browser_agent()
        
        # Test direct navigation
        print("\n📍 Direct Navigation:")
        result = browser_agent.navigate_to("https://httpbin.org/html")
        print(f"Navigation result: {result}")
        
        # Test direct screenshot with examples subdirectory
        print("\n📸 Direct Screenshot:")
        from iicrawlermcp.core.config import config
        examples_dir = config.get_screenshots_dir('examples')
        examples_dir.mkdir(parents=True, exist_ok=True)
        screenshot_path = browser_agent.take_screenshot(str(examples_dir / "demo_direct_method.png"))
        print(f"Screenshot saved to: {screenshot_path}")
        
        # Test direct page info
        print("\n📄 Direct Page Info:")
        page_info = browser_agent.get_page_info()
        print(f"Page info: {page_info}")
        
        # Clean up
        browser_agent.cleanup()
        print("\n✅ Direct methods demo completed successfully!")
        
    except Exception as e:
        logger.error(f"Direct methods demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")


def demo_complex_browser_task():
    """Demonstrate a complex browser automation task."""
    print("\n" + "="*60)
    print("🚀 BrowserAgent Complex Task Demo")
    print("="*60)
    
    try:
        # Build the browser agent
        browser_agent = build_browser_agent()
        
        # Complex task: Navigate to Google and search
        complex_task = """
        Navigate to https://google.com, 
        take a screenshot of the homepage,
        then get the page title and URL information.
        """
        
        print(f"\n🎯 Executing complex task: {complex_task.strip()}")
        result = browser_agent.invoke(complex_task)
        print(f"Result: {result['output']}")
        
        # Clean up
        browser_agent.cleanup()
        print("\n✅ Complex task demo completed successfully!")
        
    except Exception as e:
        logger.error(f"Complex task demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")


def main():
    """Main function to run all BrowserAgent demos."""
    try:
        # Validate configuration
        config.validate()
        logger.info("Configuration validated successfully")
        
        print("🎭 iICrawlerMCP BrowserAgent Demo")
        print("This demo showcases the specialized BrowserAgent capabilities")
        
        # Run all demos
        demo_basic_browser_operations()
        test_direct_browser_methods()
        demo_complex_browser_task()
        
        print("\n" + "="*60)
        print("🎉 All BrowserAgent demos completed!")
        print("="*60)
        
    except KeyboardInterrupt:
        logger.info("Demo interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
