"""
Unit tests for DOM Extractor module.

This module contains unit tests for the DOM extractor functionality,
testing individual methods and components in isolation.
"""

import pytest
import logging
from unittest.mock import Mock, patch

from iicrawlermcp.dom.core.extractor import DOMExtractor
from iicrawlermcp.core.browser import get_global_browser, close_global_browser

logger = logging.getLogger(__name__)


class TestDOMExtractor:
    """Test class for DOM Extractor functionality."""
    
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """Setup and teardown for each test."""
        # Setup
        self.browser = None
        self.extractor = None
        
        yield
        
        # Teardown
        try:
            if self.browser:
                close_global_browser()
        except Exception as e:
            logger.warning(f"Cleanup warning: {e}")
    
    def test_dom_extractor_creation(self):
        """Test that DOMExtractor can be created successfully."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        assert extractor is not None
        assert extractor.browser is browser
        
        self.browser = browser
        self.extractor = extractor
    
    def test_extract_all_elements_basic(self):
        """Test basic element extraction functionality."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        # Navigate to a simple test page
        browser.navigate("data:text/html,<html><body><h1>Test</h1><p>Content</p></body></html>")
        
        all_elements = extractor.extract_all_elements()
        assert isinstance(all_elements, list)
        assert len(all_elements) > 0
        
        self.browser = browser
        self.extractor = extractor
    
    def test_get_interactive_elements(self):
        """Test getting interactive elements."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        # Create a test page with interactive elements
        test_html = """
        <html>
        <body>
            <button>Click me</button>
            <input type="text" placeholder="Enter text">
            <a href="#test">Link</a>
            <p>Non-interactive text</p>
        </body>
        </html>
        """
        browser.navigate(f"data:text/html,{test_html}")
        
        interactive_elements = extractor.get_interactive_elements()
        assert isinstance(interactive_elements, list)
        
        # Should find button, input, and link
        interactive_tags = [elem.tag_name.lower() for elem in interactive_elements]
        assert 'button' in interactive_tags or 'input' in interactive_tags or 'a' in interactive_tags
        
        self.browser = browser
        self.extractor = extractor
    
    def test_get_clickable_elements(self):
        """Test getting clickable elements."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        test_html = """
        <html>
        <body>
            <button>Button</button>
            <a href="#test">Link</a>
            <div onclick="test()">Clickable div</div>
            <p>Non-clickable</p>
        </body>
        </html>
        """
        browser.navigate(f"data:text/html,{test_html}")
        
        clickable_elements = extractor.get_clickable_elements()
        assert isinstance(clickable_elements, list)
        
        self.browser = browser
        self.extractor = extractor
    
    def test_get_input_elements(self):
        """Test getting input elements."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        test_html = """
        <html>
        <body>
            <input type="text" placeholder="Text input">
            <input type="email" placeholder="Email input">
            <textarea placeholder="Text area"></textarea>
            <select><option>Option</option></select>
            <button>Not an input</button>
        </body>
        </html>
        """
        browser.navigate(f"data:text/html,{test_html}")
        
        input_elements = extractor.get_input_elements()
        assert isinstance(input_elements, list)
        
        # Should find input, textarea, and select elements
        input_tags = [elem.tag_name.lower() for elem in input_elements]
        expected_tags = ['input', 'textarea', 'select']
        assert any(tag in input_tags for tag in expected_tags)
        
        self.browser = browser
        self.extractor = extractor
    
    def test_find_elements_by_text(self):
        """Test finding elements by text content."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        test_html = """
        <html>
        <body>
            <h1>Search Results</h1>
            <button>Search</button>
            <p>This is a search function</p>
            <div>No match here</div>
        </body>
        </html>
        """
        browser.navigate(f"data:text/html,{test_html}")
        
        search_elements = extractor.find_elements_by_text("search", case_sensitive=False)
        assert isinstance(search_elements, list)
        assert len(search_elements) >= 2  # Should find at least h1 and button
        
        self.browser = browser
        self.extractor = extractor
    
    def test_find_buttons_with_text(self):
        """Test finding buttons with specific text."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        test_html = """
        <html>
        <body>
            <button>Search</button>
            <button>Submit</button>
            <input type="submit" value="Search">
            <p>Search text</p>
        </body>
        </html>
        """
        browser.navigate(f"data:text/html,{test_html}")
        
        search_buttons = extractor.find_buttons_with_text("Search")
        assert isinstance(search_buttons, list)
        assert len(search_buttons) >= 1  # Should find at least the button
        
        self.browser = browser
        self.extractor = extractor
    
    def test_get_elements_summary(self):
        """Test getting elements summary."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        test_html = """
        <html>
        <body>
            <h1>Title</h1>
            <button>Click</button>
            <input type="text">
            <p>Text</p>
        </body>
        </html>
        """
        browser.navigate(f"data:text/html,{test_html}")
        
        summary = extractor.get_elements_summary()
        assert isinstance(summary, dict)
        
        # Check required keys
        required_keys = [
            'total_elements', 'visible_elements', 'interactive_elements',
            'elements_in_viewport', 'clickable_elements', 'input_elements'
        ]
        for key in required_keys:
            assert key in summary
            assert isinstance(summary[key], int)
            assert summary[key] >= 0
        
        self.browser = browser
        self.extractor = extractor


class TestDOMExtractorIntegration:
    """Integration tests for DOM Extractor with real pages."""
    
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """Setup and teardown for each test."""
        self.browser = None
        yield
        try:
            if self.browser:
                close_global_browser()
        except Exception:
            pass
    
    @pytest.mark.slow
    def test_google_page_extraction(self):
        """Test DOM extraction on Google homepage."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        try:
            browser.navigate("https://www.google.com")
            
            # Test basic extraction
            all_elements = extractor.extract_all_elements()
            assert len(all_elements) > 10
            
            # Test interactive elements
            interactive_elements = extractor.get_interactive_elements()
            assert len(interactive_elements) > 0
            
            # Test summary
            summary = extractor.get_elements_summary()
            assert summary['total_elements'] > 0
            assert summary['interactive_elements'] > 0
            
        except Exception as e:
            pytest.skip(f"Network test skipped: {e}")
        
        self.browser = browser
    
    @pytest.mark.slow
    def test_github_page_extraction(self):
        """Test DOM extraction on GitHub homepage."""
        browser = get_global_browser()
        extractor = DOMExtractor(browser)
        
        try:
            browser.navigate("https://github.com")
            
            # Test navigation elements
            nav_elements = extractor.get_navigation_elements()
            assert isinstance(nav_elements, list)
            
            # Test form elements
            form_elements = extractor.get_form_elements()
            assert isinstance(form_elements, list)
            
            # Test finding sign in buttons
            sign_in_buttons = extractor.find_buttons_with_text("Sign in")
            assert isinstance(sign_in_buttons, list)
            
        except Exception as e:
            pytest.skip(f"Network test skipped: {e}")
        
        self.browser = browser


if __name__ == "__main__":
    pytest.main([__file__])
