"""
Test script to verify improved full page screenshot functionality with lazy loading.
"""

import os
import sys
import time

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.core.browser import <PERSON><PERSON><PERSON>


def test_fullpage_screenshot_with_lazy_loading():
    """Test full page screenshot with lazy loading content."""
    print("🧪 Testing full page screenshot with lazy loading...")
    
    browser = Browser(headless=True)
    
    try:
        # Test 1: Site with lazy loading images (Apple)
        print("\n📍 Test 1: Apple website (has lazy loading)")
        result = browser.navigate("https://www.apple.com")
        print(f"✅ Navigation result: {result}")
        
        # Take full page screenshot (should trigger scrolling)
        screenshot_path = browser.screenshot(full_page=True)
        print(f"📁 Full page screenshot saved to: {screenshot_path}")
        
        if os.path.exists(screenshot_path):
            file_size = os.path.getsize(screenshot_path)
            print(f"✅ Screenshot exists, size: {file_size} bytes")
        else:
            print("❌ Screenshot not found")
            return False
        
        time.sleep(2)
        
        # Test 2: Compare with viewport screenshot
        print("\n📍 Test 2: Viewport screenshot comparison")
        viewport_path = browser.screenshot(full_page=False)
        print(f"📁 Viewport screenshot saved to: {viewport_path}")
        
        if os.path.exists(viewport_path):
            full_size = os.path.getsize(screenshot_path)
            viewport_size = os.path.getsize(viewport_path)
            print(f"📊 Full page size: {full_size} bytes")
            print(f"📊 Viewport size: {viewport_size} bytes")
            
            if full_size > viewport_size:
                print("✅ Full page screenshot is larger than viewport (expected)")
            else:
                print("⚠️ Full page screenshot is not larger than viewport")
        
        time.sleep(2)
        
        # Test 3: Different site with content
        print("\n📍 Test 3: GitHub website")
        result = browser.navigate("https://github.com")
        print(f"✅ Navigation result: {result}")
        
        github_screenshot = browser.screenshot(full_page=True)
        print(f"📁 GitHub full page screenshot: {github_screenshot}")
        
        if os.path.exists(github_screenshot):
            print("✅ GitHub screenshot captured successfully")
        else:
            print("❌ GitHub screenshot failed")
            return False
        
        time.sleep(2)
        
        # Test 4: Long content page
        print("\n📍 Test 4: Long content page (Wikipedia)")
        result = browser.navigate("https://en.wikipedia.org/wiki/Web_scraping")
        print(f"✅ Navigation result: {result}")
        
        wiki_screenshot = browser.screenshot(full_page=True)
        print(f"📁 Wikipedia full page screenshot: {wiki_screenshot}")
        
        if os.path.exists(wiki_screenshot):
            file_size = os.path.getsize(wiki_screenshot)
            print(f"✅ Wikipedia screenshot exists, size: {file_size} bytes")
        else:
            print("❌ Wikipedia screenshot failed")
            return False
        
        print("\n📋 Summary of screenshots:")
        screenshots = [screenshot_path, viewport_path, github_screenshot, wiki_screenshot]
        for i, path in enumerate(screenshots, 1):
            if os.path.exists(path):
                size = os.path.getsize(path)
                filename = os.path.basename(path)
                print(f"  {i}. {filename} ({size} bytes)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        browser.close()
        print("🔒 Browser closed")


def test_custom_vs_fullpage():
    """Test difference between custom path and full page behavior."""
    print("\n🧪 Testing custom path vs full page behavior...")
    
    browser = Browser(headless=True)
    
    try:
        # Navigate to a content-rich page
        result = browser.navigate("https://httpbin.org/html")
        print(f"✅ Navigation result: {result}")
        
        # Test custom path with full page
        custom_full_path = "../../screenshots/test_custom_fullpage.png"
        result1 = browser.screenshot(custom_full_path, full_page=True)
        print(f"📁 Custom full page: {result1}")
        
        # Test custom path with viewport
        custom_viewport_path = "../../screenshots/test_custom_viewport.png"
        result2 = browser.screenshot(custom_viewport_path, full_page=False)
        print(f"📁 Custom viewport: {result2}")
        
        # Test default path with full page
        result3 = browser.screenshot(full_page=True)
        print(f"📁 Default full page: {result3}")
        
        # Verify all files exist
        paths = [custom_full_path, custom_viewport_path, result3]
        all_exist = True
        for path in paths:
            if os.path.exists(path):
                size = os.path.getsize(path)
                print(f"✅ {os.path.basename(path)}: {size} bytes")
            else:
                print(f"❌ Missing: {path}")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Custom test failed: {e}")
        return False
        
    finally:
        browser.close()


def list_all_screenshots():
    """List all screenshots in the directory."""
    print("\n📁 All screenshots in directory:")
    screenshots_dir = "../../screenshots"
    
    if os.path.exists(screenshots_dir):
        files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
        files.sort()
        
        total_size = 0
        for file in files:
            file_path = os.path.join(screenshots_dir, file)
            file_size = os.path.getsize(file_path)
            total_size += file_size
            print(f"  - {file} ({file_size:,} bytes)")
        
        print(f"\n📊 Total: {len(files)} files, {total_size:,} bytes")
    else:
        print("  Screenshots directory not found")


if __name__ == "__main__":
    print("🚀 Starting full page screenshot tests with lazy loading...\n")
    
    # Test full page with lazy loading
    success1 = test_fullpage_screenshot_with_lazy_loading()
    
    # Test custom vs full page
    success2 = test_custom_vs_fullpage()
    
    # List all screenshots
    list_all_screenshots()
    
    if success1 and success2:
        print("\n✅ All full page screenshot tests passed!")
        print("📝 Note: Full page screenshots now include lazy-loaded content!")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
