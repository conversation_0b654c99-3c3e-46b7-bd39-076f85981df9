"""
Integration tests for LLM connection validation.

This module provides integration tests to verify that the LangChain integration
with the configured LLM is working correctly.
"""

import pytest
import logging

from iicrawlermcp.core.config import config
from iicrawlermcp.agents.crawler_agent import build_agent

logger = logging.getLogger(__name__)


class TestLLMConnection:
    """Test class for LLM connection validation."""
    
    def test_config_validation(self):
        """Test that configuration validation works correctly."""
        try:
            config.validate()
            assert True, "Configuration validation passed"
        except ValueError as e:
            pytest.fail(f"Configuration validation failed: {e}")
    
    def test_llm_connection(self):
        """Test basic LLM connection and response."""
        try:
            # Build agent (this will create and test LLM connection)
            agent = build_agent()
            
            # Send a simple test message
            result = agent.invoke("Hello, this is a test. Please reply with 'OK'.")
            
            # Verify we got a response
            assert "output" in result
            assert result["output"] is not None
            assert len(result["output"]) > 0
            
            logger.info(f"LLM Response: {result['output']}")
            print(f"\n✅ LLM Connection Test Passed!")
            print(f"Response: {result['output']}")
            
            # Clean up
            agent.cleanup()
            
        except Exception as e:
            pytest.fail(f"LLM connection test failed: {e}")
    
    def test_agent_creation(self):
        """Test that agent can be created successfully."""
        try:
            agent = build_agent()
            assert agent is not None
            
            # Test that agent has required components
            assert agent.tools is not None
            assert len(agent.tools) > 0
            
            agent.cleanup()
            
        except Exception as e:
            pytest.fail(f"Agent creation test failed: {e}")


class TestAgentIntegration:
    """Test class for agent integration functionality."""
    
    def test_agent_with_browser_tools(self):
        """Test that agent can use browser tools."""
        try:
            agent = build_agent()
            
            # Test that browser tools are available
            tool_names = [tool.name for tool in agent.tools]
            expected_tools = ['navigate', 'screenshot', 'browser_snapshot']
            
            for tool in expected_tools:
                assert tool in tool_names, f"Tool '{tool}' not found in agent tools"
            
            agent.cleanup()
            
        except Exception as e:
            pytest.fail(f"Agent browser tools test failed: {e}")
    
    def test_agent_simple_task(self):
        """Test agent with a simple task."""
        try:
            agent = build_agent()
            
            # Test a simple navigation task
            result = agent.invoke("Navigate to https://httpbin.org/html")
            
            # Verify we got a response
            assert "output" in result
            assert result["output"] is not None
            
            logger.info(f"Agent task result: {result['output']}")
            
            agent.cleanup()
            
        except Exception as e:
            pytest.skip(f"Agent task test skipped due to network/API issues: {e}")


def test_connection_standalone():
    """
    Standalone function to test LLM connection.
    
    This function can be run independently to verify the connection
    without using pytest.
    """
    try:
        print("正在初始化配置...")
        config.validate()
        print("✅ 配置验证成功")
        
        print("正在创建代理...")
        agent = build_agent()
        print("✅ 代理创建成功")
        
        print("正在发送测试消息...")
        result = agent.invoke("Hello, this is a test. Please reply with 'OK'.")
        
        print("LLM 响应:")
        print(result["output"])
        print("\n✅ 测试成功！LangChain 可以连接到您的 LLM。")
        
        # Clean up
        agent.cleanup()
        
    except Exception as e:
        print(f"❌ 测试失败！发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    test_connection_standalone()
