#!/usr/bin/env python3
"""
测试所有可配置参数功能
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_all_configurable_params():
    """
    测试所有可配置参数功能
    """
    
    print("🔧 所有可配置参数功能测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        # 显示当前所有配置
        highlight_config = config.get_highlight_config()
        extraction_config = config.get_dom_extraction_config()
        
        print("\n🎨 高亮框配置：")
        for key, value in highlight_config.items():
            if key == 'colors':
                print(f"   - {key}: {len(value)} 种颜色")
            else:
                print(f"   - {key}: {value}")
        
        print("\n🔍 DOM提取配置：")
        for key, value in extraction_config.items():
            print(f"   - {key}: {value}")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 测试任务：使用所有配置参数
        task_description = """
        请执行以下任务来测试所有可配置参数：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 分析页面上的表单元素（使用所有配置化参数）
        3. 截取页面截图，展示配置化效果
        
        请确保使用.env文件中的所有配置参数，包括：
        - 高亮框样式配置
        - DOM提取配置
        - 调试模式配置
        """
        
        print("\n🚀 开始所有配置参数测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"All configurable params test error: {e}")
        return False


def test_config_parameter_details():
    """
    测试配置参数详细信息
    """
    
    print("📋 配置参数详细信息")
    print("=" * 50)
    
    try:
        config.validate()
        
        print("\n🎨 高亮框配置详情：")
        print(f"   - 启用状态: {config.DOM_HIGHLIGHT_ENABLED}")
        print(f"   - 边框宽度: {config.DOM_HIGHLIGHT_BORDER_WIDTH}px")
        print(f"   - 透明度: {config.DOM_HIGHLIGHT_OPACITY}")
        print(f"   - Z-Index: {config.DOM_HIGHLIGHT_Z_INDEX}")
        print(f"   - 标签字体大小: {config.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MIN}-{config.DOM_HIGHLIGHT_LABEL_FONT_SIZE_MAX}px")
        print(f"   - 标签内边距: {config.DOM_HIGHLIGHT_LABEL_PADDING}")
        print(f"   - 标签圆角: {config.DOM_HIGHLIGHT_LABEL_BORDER_RADIUS}")
        
        colors = config.DOM_HIGHLIGHT_COLORS.split(',')
        print(f"   - 颜色数量: {len(colors)}")
        print(f"   - 前5种颜色: {', '.join(colors[:5])}")
        
        print("\n🔍 DOM提取配置详情：")
        print(f"   - 焦点高亮索引: {config.DOM_FOCUS_HIGHLIGHT_INDEX}")
        print(f"   - 视口扩展: {config.DOM_VIEWPORT_EXPANSION}px")
        print(f"   - 调试模式: {config.DOM_DEBUG_MODE}")
        
        print("\n📖 参数说明：")
        print("   焦点高亮索引:")
        print("     -1: 高亮所有元素")
        print("     >=0: 只高亮指定索引的元素")
        print("   视口扩展:")
        print("     0: 精确视口")
        print("     >0: 扩展视口(像素)")
        print("     -1: 所有元素(忽略视口)")
        print("   调试模式:")
        print("     true: 启用控制台日志")
        print("     false: 禁用日志")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 配置详情获取失败: {e}")
        logger.error(f"Config details error: {e}")
        return False


def main():
    """主函数"""
    
    print("🔧 所有可配置参数功能测试")
    print("=" * 60)
    print("验证.env文件中的所有DOM配置参数")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 所有配置参数功能测试")
    print("2. 配置参数详细信息")
    
    choice = input("\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n🔧 执行所有配置参数功能测试...")
        result = test_all_configurable_params()
    elif choice == "2":
        print("\n📋 显示配置参数详细信息...")
        result = test_config_parameter_details()
    else:
        print("👋 无效选择，显示配置参数详细信息...")
        result = test_config_parameter_details()
    
    if result:
        print("\n🎉 所有配置参数测试成功！")
        print("\n💡 实现的完整配置功能：")
        print("• ✅ 高亮框样式 - 完全可配置")
        print("• ✅ DOM提取行为 - 完全可配置")
        print("• ✅ 调试功能 - 可配置启用/禁用")
        print("• ✅ 视口控制 - 灵活的视口策略")
        print("• ✅ 焦点高亮 - 可选择性高亮")
        
        print("\n🔧 所有配置参数：")
        print("• DOM_HIGHLIGHT_* - 高亮框外观配置")
        print("• DOM_FOCUS_HIGHLIGHT_INDEX - 焦点高亮控制")
        print("• DOM_VIEWPORT_EXPANSION - 视口扩展策略")
        print("• DOM_DEBUG_MODE - 调试模式开关")
        
        return 0
    else:
        print("\n❌ 所有配置参数测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
