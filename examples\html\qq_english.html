<!DOCTYPE html>
<html lang="zh">
<head>


<meta charset="utf-8">
<!--[if lt IE 9]>
<script src="/k/static/0/js/lib/html5.js"></script>
<![endif]-->
<link href="/k/static/0/fonts/icomoon.css" rel="stylesheet" type="text/css">
<link href="/k/static/135/css/main.css?v=64" rel="stylesheet" type="text/css">
<link href="/k/static/135/css/site.css?v=37" rel="stylesheet" type="text/css">
<link href="/k/static/135/css/swiper.min.css" rel="stylesheet" type="text/css">
<link href="/k/static/135/css/examPage.css?v=20200402" rel="stylesheet" type="text/css">
<!--[if lt IE 9]>
<link href="/k/static/135/css/main.ie8.css" rel="stylesheet" type="text/css">
<![endif]-->
<title>My Lesson｜快酷英语</title>








    <style>
        .ai_curriculum_lesson_time,
        .normal_curriculum_lesson_time {
            display: inline-block;
        }
    </style>
</head>
<body class="page-mypage-top">
<!--header-->

<header><div class="inner">

    <div class="logo"><a href="https://adult.kuaikuenglish.com/k/mypage/">快酷英语</a></div>


    <div class="clock" clock-now="01,40,38,CST">
        <i class="icon-clock"></i>
        <span>01:40:38 CST</span>
    </div>


    <ul class="nav">






<li class="ai_study relative" name="ai_study">
    <span class="ai_icon_flag" style="left: 2%">
       <img src="/k/static/135/images/common/ai_icon.png" class="">
    </span>
    <a href="javascript:void(0)"><i style="font-size: 16px" class="icon-study"></i> AI陪练<i class="icon-header-nav-arrow-down"></i></a>
    <ul class="children" style="display:none;width: 160px">
        <li class="notices_top" name="notices_top">
            <a href="https://adult.kuaikuenglish.com/k/mypage/ai/curriculum/customized/#/">
                <img style="width: 13px;" src="/k/static/135/images/common/aigc_icon.png" class="">
                 AI 定制课
                <img style="width: 22%;margin-top: -8px;margin-left: -4px" src="/k/static/135/images/ai_coach_test/ai_test_new.png" class="">
            </a>

        </li>

        <li class="gpt_composition relative" >
            <a href="https://adult.kuaikuenglish.com/k/gpt/composition">
                <i class="icon-pencil"></i>

                英文写作批改

            </a>
        </li>


    </ul>
</li>
        <li class="mylesson mylesson2" name="mylesson">
            <a href="https://adult.kuaikuenglish.com/k/mypage/">我的课程 <i class="icon-header-nav-arrow-down"></i></a>
            <ul class="children" style="display:none;">
                <li name="reserve">
                    <a href="https://adult.kuaikuenglish.com/k/searcher/schedules/">预约课程 <i class="icon-header-nav-arrow-right"></i></a>
                    <ul class="children" style="display:none;">
                        <li><a href="https://adult.kuaikuenglish.com/k/searcher/schedules/">查看课表</a></li>
                        <li><a href="https://adult.kuaikuenglish.com/k/searcher/teachers/">全体老师</a></li>

                        <li><a href="/k/searcher/fixings/">固定老师搜索</a></li>

                    </ul>
                </li>
                <li><a href="https://adult.kuaikuenglish.com/k/mypage/lessons/reserved/">已约课程</a></li>
                <li><a href="https://adult.kuaikuenglish.com/k/mypage/ai/curriculum/customized/#/preview_list">我的AI定制课</a></li>
                <li><a href="https://adult.kuaikuenglish.com/k/mypage/lessons/finished/">学习记录</a></li>
                <li><a href="https://adult.kuaikuenglish.com/k/mypage/lessons/canceled/">已取消课程</a></li>

                <li><a href="https://adult.kuaikuenglish.com/k/mypage/progress/">课程进度</a></li>



                <li><a href="http://www.kuaikuenglish.com/static/download/index.shtml" target="_blank">下载教材</a></li>





            </ul>
        </li>

        <li class="account" name="account">
            <a href="#account"><img src="/k/cache/images/student/00/00/noimage_m.36x36.cut.png" class="thumb mr6">Eren <i class="icon-header-nav-arrow-down"></i></a>
            <ul class="children" style="display:none;">
                <li><a href="https://adult.kuaikuenglish.com/k/mypage/settings/account/">账号管理</a></li>
                <li><a href="https://payment-adult.kuaikuenglish.com/account/points?email=<EMAIL>&uid=BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54" target="_blank">积分商城</a></li>



                <li><a href="https://adult.kuaikuenglish.com/k/points/">课时点记录</a></li>


                <li><a href="https://adult.kuaikuenglish.com/k/mypage/ticket/">课程兑换码和次卡</a></li>


<li><a href="https://adult.kuaikuenglish.com/k/mypage/textbook/certificates/" >证书</a></li>
                <li><a href="https://adult.kuaikuenglish.com/k/logout/" class="clear_token">退出</a></li>
            </ul>
        </li>
        <li class="help" name="help">
            <a href="https://adult.kuaikuenglish.com/k/support/">客服 <i class="icon-header-nav-arrow-down"></i></a>
            <ul class="children" style="display:none;">
                <li><a href="https://adult.kuaikuenglish.com/k/faq/">常见问题</a></li>
                <li><a href="https://adult.kuaikuenglish.com/k/support/">意见反馈</a></li>
            </ul>
        </li>


<li class="puzzle_game puzzle_game_content" name="puzzle_game">

    <a href="https://adult.kuaikuenglish.com/k/game/puzzle/">
        世界知识探秘
    </a>

</li>

<li class="notifications notices_center" name="notifications">
    <a href="https://adult.kuaikuenglish.com/k/q_messages/"><i class="icon-sound white"></i> 通知中心<i class="icon-header-nav-arrow-down"></i></a>
    <div class="q_messages_unread_count"></div>
    <ul class="children" style="display:none;width: 160px">
        <li class="notices_top" name="notices_top">
            <a href="https://adult.kuaikuenglish.com/k/q_messages/">
                <i class=" icon-bubbles4 white"></i> 重要提醒
            </a>
            <div class="q_messages_unread_count"></div>
        </li>
        <li>
            <a href="https://adult.kuaikuenglish.com/k/informations/"><i class=" icon-bell2 white"></i> 系统消息</a>
        </li>
    </ul>
</li>

    </ul>


</div></header>

<h1 class="heading">My Lesson</h1>
<div class="information_top_content vm" style="display: none">
    <div class="mr15 in_title">系统消息</div>
    <div class="text_content">
        <ul class="notice_list_top"></ul>
    </div>
    <div class="more_right mr15"><a href="/k/informations/">更多 <i class="icon-arrow-right"></i></a></div>
</div>
<div class="dialog" id="information_dialog" style="display:none; width:670px; height:480px;">
    <div class="header"><span style="z-index: 999999;position: absolute;left: 45%;">
         <i class="icon-bell2 top-1px mr6"></i>系统消息
    </span></div>
    <div class="content" >
        <div class="inner">
            <div class="bold blue f16px m20 text-center mt10 mb10" style="text-align: center">
                <div class="information_title"></div>
            </div>
            <div style="margin: 0 auto;text-align: center">
                <div style="text-align: left" class="information_content"></div>
                <div style="text-align: left" class="information_links mt5"></div>
            </div>
        </div>
    </div>
    <div class="footer">
        <a href="" class="btn gray w100 ml2 change_close" dialog-close="1">关闭</a>
    </div>

    <a href="" class="close notice_close" dialog-close="1"></a>
</div>
<div class="contents mb20">
    <div class="left">



<div banners-async="top" class="top_banner_content" >
    <div class="swiper-container" id="swiper-container-top" >
        <div class="swiper-wrapper" id="swiper-wrapper-top" ></div>
        <div id="swiper-pagination-top" class="swiper-pagination"></div>
        <div id="swiper-button-prev-top" class="swiper-button-prev swiper_hide" ><i class="icon-arrow-left"></i></div>
        <div id="swiper-button-next-top" class="swiper-button-next swiper_hide"><i class="icon-arrow-right"></i></div>
    </div>
</div>






















            <div style="display:none" name="top_2column_content" class="left">




<section class="latestlesson">
    <h2 class="heading m10"><a href="/k/mypage/lessons/reserved/"><i class="icon-calendar3"></i> 最近预约</a></h2>
    <dl>
        <dt>

            <a href="/k/teacher/48770340"><img src="/k/cache/images/teacher/8a/07/305d8611af6fc51457a8d0b7b1895d3f17a28a07.216x216.cut.png" alt="Gunther" class="thumb"></a>

        </dt>
        <dd>
            <div class="f14px bold blue vm mb5"><i class="icon-clock mr3 top-1px"></i>2025-08-01<span class="small ml6">18:00-18:30</span></div>

            <div class="mb5"><a href="/k/teacher/48770340" class="bold bluegray block mb3">Gunther<i class="icon-teacher gray ml4"></i></a></div>


            <div class="gray small"><i class="icon-pencil2 op50 mr3"></i>Basic English</div>

        </dd>
    </dl>
    <a href="/k/mypage/lesson/55412520/request/" class="btn gray small more w210">给老师留言</a>
</section>


            </div>
            <div style="display:none" name="top_2column_content" class="right">



<section class="pointsbox">
    <h2 class="heading m10"><i class="icon-coin f17px mr1 top-1px"></i> 课时点余额</h2>
    <div class="point"><strong>1,972</strong>pts</div>
    <p class="description">查看课时点记录</p>
    <a href="/k/points/" class="btn small blue more">课时点记录</a>


            <a href="/k/points/" class="btn small blue more">课时点记录</a>




</section>


            </div>










        <div class="single">




<section class="searchbox pt75">
    <ul class="tabs">
        <li class="schedule active" target="schedule"><a href="/k/searcher/schedules/"><i class="icon-schedule"></i>按时间搜索</a></li>
        <li class="teacher " target="teacher"><a href="/k/searcher/teachers/"><i class="icon-teacher"></i>按老师搜索</a></li>

        <li class="curriculum " target="curriculum"><a href="/k/searcher/fixings/"><i class="icon-curriculum"></i>固定老师搜索</a></li>

    </ul>

    <div class="content schedule" name="schedule" >

    <div class="single single_style_position_left pt5">
        <section class="box balloon left-side-style  r5 balloon-intro">
            <span>您可以选择喜欢的时间、老师预约课程</span>

        </section>
    </div>





<form name="searchbox_schdule_f" id="searchbox_schdule_f" action="" method="get">
<input type="hidden" name="from_search" value="1" />
<div class="palette" current-date="2025-07-28" current-time-span="1">
    <ul class="dates">
        <li class="prev" show-prev-week="1"><a href="#prev"><i class="icon-arrow-left"></i></a></li>
        <li class="selection">
            <ul class="inner" dates="1">

                <li date="2025-07-27" class="disabled">
                <a href="#2025-07-27">
                <strong>7/27</strong>
                <em>星期日</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-07-28" class="">
                <a href="#2025-07-28">
                <strong>7/28</strong>
                <em>星期一</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-07-29" class="">
                <a href="#2025-07-29">
                <strong>7/29</strong>
                <em>星期二</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-07-30" class="">
                <a href="#2025-07-30">
                <strong>7/30</strong>
                <em>星期三</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-07-31" class="">
                <a href="#2025-07-31">
                <strong>7/31</strong>
                <em>星期四</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-01" class="">
                <a href="#2025-08-01">
                <strong>8/1</strong>
                <em>星期五</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-02" class="">
                <a href="#2025-08-02">
                <strong>8/2</strong>
                <em>星期六</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-03" class="">
                <a href="#2025-08-03">
                <strong>8/3</strong>
                <em>星期日</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-04" class="">
                <a href="#2025-08-04">
                <strong>8/4</strong>
                <em>星期一</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-05" class="">
                <a href="#2025-08-05">
                <strong>8/5</strong>
                <em>星期二</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-06" class="">
                <a href="#2025-08-06">
                <strong>8/6</strong>
                <em>星期三</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-07" class="">
                <a href="#2025-08-07">
                <strong>8/7</strong>
                <em>星期四</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-08" class="">
                <a href="#2025-08-08">
                <strong>8/8</strong>
                <em>星期五</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-09" class="">
                <a href="#2025-08-09">
                <strong>8/9</strong>
                <em>星期六</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-10" class="">
                <a href="#2025-08-10">
                <strong>8/10</strong>
                <em>星期日</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-11" class="disabled">
                <a href="#2025-08-11">
                <strong>8/11</strong>
                <em>星期一</em>
                </a>
                <div>







                </div>
                </li>

                <li date="2025-08-12" class="disabled">
                <a href="#2025-08-12">
                <strong>8/12</strong>
                <em>星期二</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-13" class="disabled">
                <a href="#2025-08-13">
                <strong>8/13</strong>
                <em>星期三</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-14" class="disabled">
                <a href="#2025-08-14">
                <strong>8/14</strong>
                <em>星期四</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-15" class="disabled">
                <a href="#2025-08-15">
                <strong>8/15</strong>
                <em>星期五</em>
                </a>
                <div>



                </div>
                </li>

                <li date="2025-08-16" class="disabled">
                <a href="#2025-08-16">
                <strong>8/16</strong>
                <em>星期六</em>
                </a>
                <div>



                </div>
                </li>

            </ul>
        </li>
        <li class="next" show-next-week="1"><a href="#next"><i class="icon-arrow-right"></i></a></li>
    </ul>
    <ul class="times">
        <li time-span="1"><a href="#0:00-8:00">0:00-8:00</a></li>
        <li time-span="2"><a href="#6:00-14:00">6:00-14:00</a></li>
        <li time-span="3"><a href="#12:00-20:00">12:00-20:00</a></li>
        <li time-span="4"><a href="#18:00-25:00">18:00-25:00</a></li>
        <li time-span="0"><a href="#24h">24小时</a></li>
    </ul>
    <input type="hidden" name="date" value="2025-07-28">
    <input type="hidden" name="time_span" value="1">
</div>
<dl class="field top">
    <dt>老师</dt>
    <dd>
        <div class="form-radio" name="mode" value="all" label="展示全部"></div>
        <div class="form-radio" name="mode" value="bookmarked" label="我的收藏" ></div>
        <div class="form-radio" name="mode" value="taken" label="曾经预约" ></div>

        <input type="hidden" name="mode" value="all">
        <ul class="inline mt5">
            <li><div class="form-checkbtn" name="callan" value="1" label="凯伦方法"><input type="hidden" name="callan"></div></li>
            <li><div class="form-checkbtn" name="long_career" value="1" label="三年以上教龄"><input type="hidden" name="long_career"></div></li>
            <li><div class="form-checkbtn" name="business" value="1" label="商务英语"><input type="hidden" name="business"></div></li>
            <li><div class="form-checkbtn" name="kids" value="1" label="有亲和力"><input type="hidden" name="kids"></div></li>
            <li><div class="form-checkbtn" name="male" value="1" label="男性"><input type="hidden" name="male"></div></li>
            <li><div class="form-checkbtn" name="female" value="1" label="女性"><input type="hidden" name="female"></div></li>

            <li><div class="form-checkbtn" name="fixed" value="1" label="固定老师预约"><input type="hidden" name="fixed"></div></li>


            <li><div class="form-checkbtn" name="group" value="1" label="小班课"><input type="hidden" name="group"></div></li>

        </ul>
    </dd>
</dl>
<dl class="field">
    <dt>课程（*必选）</dt>
    <dd style="position: relative">

<div class="search_content_div">
<div class="mb10 radio_content">
    <div style="cursor: pointer"  class="form-radio" name="curriculum_type" value="normal_curriculum" label="常规课程"></div>
    <div style="cursor: pointer" class="form-radio aigc_curriculum_radio" name="curriculum_type" value="ai_curriculum" label="AI定制课"></div>
</div>
<div data="normal_lesson" class="normal_curriculum_lesson_time">
    <div style="cursor: pointer"  class="form-radio tip_click_one" name="lesson_time" value="30" label="25分钟"></div>
    <div style="cursor: pointer" class="form-radio tip_click_two" name="lesson_time" value="60" label="50分钟"></div>

</div>
<div data="aigc_lesson" class="ai_curriculum_lesson_time" style="display:none;">

    <div style="cursor: pointer"  class="form-radio" name="aigc_lesson_time" value="30" label="25分钟"></div>

</div>
<div data="normal_lesson" style="display: inline-block">



</div>
<div data="aigc_lesson" style="display: inline-block">



</div>
<input  type="hidden" name="site_label" value="cnadt">
<input data="normal_lesson" type="hidden" name="lesson_time" value="30">

    <input data="aigc_lesson" type="hidden" name="aigc_lesson_time" value="30">

<input type="hidden" name="curriculum_type" value="normal_curriculum">

<input type="hidden" name="aigc_curriculum_id" value="1002759">

<input type="hidden" name="aigc_curriculum_id" value="1002784">

<div data="normal_lesson" style="display: inline-block">
<div  class="form-select ml10 w200 select_curriculum" name="curriculum" input-keys="curriculum" placeholder="选择课程">
    <div style="display:none;">


        <button value="1002672" lesson-time="30" label="青少儿主修课Smart Juniors ">青少儿主修课Smart Juniors </button>



        <button value="1002288" lesson-time="30" label="Good English典范英语(1-6)">Good English典范英语(1-6)</button>



        <button value="1002289" lesson-time="30" label="Good English 7 （典范英语 7）">Good English 7 （典范英语 7）</button>



        <button value="1002290" lesson-time="30" label="Good English 8 （典范英语 8）">Good English 8 （典范英语 8）</button>



        <button value="1002291" lesson-time="30" label="Good English 9 (典范英语 9)">Good English 9 (典范英语 9)</button>



        <button value="1002294" lesson-time="30" label="Write Source 英语写作">Write Source 英语写作<span class="orange ml3">(+25pts)</span></button>



        <button value="1002295" lesson-time="30" label="Time to Talk">Time to Talk</button>



        <button value="1002302" lesson-time="30" label="Basic English">Basic English</button>



        <button value="1002304" lesson-time="30" label="新主题对话">新主题对话</button>



        <button value="1002305" lesson-time="30" label="新主题对话(初级) New Topic Conversation for Beginners">新主题对话(初级) New Topic Conversation for Beginners</button>



        <button value="1002569" lesson-time="30" label="English Phonetics 音标课">English Phonetics 音标课</button>



        <button value="1002307" lesson-time="30" label="凯伦方法">凯伦方法</button>



        <button value="1002308" lesson-time="30" label="商务凯伦（限Stage8以上水平）">商务凯伦（限Stage8以上水平）</button>



        <button value="1002328" lesson-time="30" label="新版商务英语">新版商务英语</button>



        <button value="1002309" lesson-time="30" label="	商务凯伦（限Stage8以上水平）">	商务凯伦（限Stage8以上水平）</button>



        <button value="1002312" lesson-time="30" label="Global Business Skils">Global Business Skils</button>



        <button value="1002314" lesson-time="30" label="旅游英语">旅游英语</button>



        <button value="1002315" lesson-time="30" label="Welcome Aboard">Welcome Aboard</button>



        <button value="1002313" lesson-time="30" label="News Alert新闻英语">News Alert新闻英语</button>



        <button value="1002318" lesson-time="60" label="50分钟TOEIC强化 ">50分钟TOEIC强化 <span class="orange ml3">(+50pts)</span></button>



        <button value="1002319" lesson-time="60" label="TOEIC Vocabulary">TOEIC Vocabulary<span class="orange ml3">(+50pts)</span></button>



        <button value="1002320" lesson-time="60" label="TOEIC Speaking">TOEIC Speaking<span class="orange ml3">(+50pts)</span></button>



        <button value="1002322" lesson-time="60" label="雅思写作 50 mins">雅思写作 50 mins<span class="orange ml3">(+50pts)</span></button>



        <button value="1002323" lesson-time="60" label="雅思口语30天">雅思口语30天<span class="orange ml3">(+50pts)</span></button>



        <button value="1002874" lesson-time="30" label="SDG Topics">SDG Topics</button>



        <button value="1002310" lesson-time="30" label="R.E.M.S.">R.E.M.S.</button>



        <button value="1002326" lesson-time="30" label="Beginner Writing">Beginner Writing<span class="orange ml3">(+25pts)</span></button>



        <button value="1002306" lesson-time="30" label="新日常生活英语">新日常生活英语</button>



        <button value="1002399" lesson-time="60" label="雅思听力 40 Days">雅思听力 40 Days</button>



        <button value="1002400" lesson-time="60" label="雅思阅读 (Academic) 40 Days">雅思阅读 (Academic) 40 Days<span class="orange ml3">(+50pts)</span></button>



        <button value="1002401" lesson-time="30" label="Wonder Words">Wonder Words</button>



        <button value="1002444" lesson-time="60" label="雅思写作 Writing Task 1 (Academic)">雅思写作 Writing Task 1 (Academic)<span class="orange ml3">(+50pts)</span></button>



        <button value="1002461" lesson-time="60" label="雅思写作 Writing Task 2">雅思写作 Writing Task 2<span class="orange ml3">(+50pts)</span></button>



        <button value="1002676" lesson-time="60" label="菲律宾游学说明会">菲律宾游学说明会<span class="orange ml3">(+99999pts)</span></button>



        <button value="1002880" lesson-time="60" label="IELTS Speaking MasterCourse">IELTS Speaking MasterCourse<span class="orange ml3">(+50pts)</span></button>



        <button value="1002894" lesson-time="60" label="IELTS Speaking Practice Test and Feedback">IELTS Speaking Practice Test and Feedback<span class="orange ml3">(+50pts)</span></button>


    </div>
    <input type="hidden" id="curriculum" name="curriculum">
</div>
</div>


<input type="hidden" name="aigc_curriculum_id" class="aigc_curriculum_id" value="1002759">

<input type="hidden" name="aigc_curriculum_id" class="aigc_curriculum_id" value="1002784">


<input type="hidden" name="aigc_curriculum_time_id" class="aigc_curriculum_time_id" value="30">

<input type="hidden" name="aigc_curriculum_time_id" class="aigc_curriculum_time_id" value="30">










<div data="aigc_lesson" class="form-select ml10 w280" style="display:none;"  name="select_courseware" value="" placeholder="请选择一个AI定制课" id="select_courseware_form">

</div>
</div>
<input type="hidden" class="can_use_ai_writing_correction" value="1">
        <p class="error mt5" style="display:none;" error-keys="curriculum"></p>
        <p class="error mt5" style="display:none;" error-keys="courseware"></p>

<div class="single tip_content_one" style="position: absolute;top: 15px;left: 0px;display: none;">
    <section class="box  pl10 pr10 left-side-min-tip min_section_tip r5 balloon-intro">
        <span class="min_tip">标准课程，时间为25分钟一节课。</span>
    </section>
</div>

<div class="single tip_content_two" style="position: absolute;top: 15px;left: 0px;display: none">
    <section class="box pl10 pr10  left-side-min-tip min_section_tip r5 balloon-intro">
        <span class="min_tip">高阶专项课程或国际小班课，时间为50分钟一节课。</span>
    </section>
</div>

    </dd>
</dl>
<dl class="field">
    <dt>时间</dt>
    <dd>
        <div class="form-select" name="time_from_to" placeholder="请选择课程时间">
            <div style="display:none;">
                <button value="" class="lightgray">未选择</button>

                <button value="00:00-01:00">0:00 - 1:00</button>

                <button value="01:00-02:00">1:00 - 2:00</button>

                <button value="02:00-03:00">2:00 - 3:00</button>

                <button value="03:00-04:00">3:00 - 4:00</button>

                <button value="04:00-05:00">4:00 - 5:00</button>

                <button value="05:00-06:00">5:00 - 6:00</button>

                <button value="06:00-07:00">6:00 - 7:00</button>

                <button value="07:00-08:00">7:00 - 8:00</button>

                <button value="08:00-09:00">8:00 - 9:00</button>

                <button value="09:00-10:00">9:00 - 10:00</button>

                <button value="10:00-11:00">10:00 - 11:00</button>

                <button value="11:00-12:00">11:00 - 12:00</button>

                <button value="12:00-13:00">12:00 - 13:00</button>

                <button value="13:00-14:00">13:00 - 14:00</button>

                <button value="14:00-15:00">14:00 - 15:00</button>

                <button value="15:00-16:00">15:00 - 16:00</button>

                <button value="16:00-17:00">16:00 - 17:00</button>

                <button value="17:00-18:00">17:00 - 18:00</button>

                <button value="18:00-19:00">18:00 - 19:00</button>

                <button value="19:00-20:00">19:00 - 20:00</button>

                <button value="20:00-21:00">20:00 - 21:00</button>

                <button value="21:00-22:00">21:00 - 22:00</button>

                <button value="22:00-23:00">22:00 - 23:00</button>

                <button value="23:00-24:00">23:00 - 24:00</button>

                <button value="24:00-25:00">24:00 - 25:00</button>

            </div>
            <input type="hidden" name="time_from_to">
        </div>
    </dd>
</dl>
<dl class="field">
    <dt>关键词</dt>
    <dd>
        <input type="text" name="keyword" class="w350" placeholder="关键词">
    </dd>
</dl>
<a name="results"></a>

        <ul class="btns">


    <li class="tl"><a href="/k/group/"><i class="icon-redo2 mr3 ml10"></i>查看小班课表</a></li>

    <li>
        <a href="javascript:void(0)" class="btn to_schedule_page red w200" ><i class="icon-search mr5 f12px"></i>搜索</a>

    </li>

</ul>
</form>


    </div><!--/schedule-->

    <div class="content teacher" name="teacher" style="display:none;">

    <div class="single single_style_position pt5">
        <section class="box balloon r5 balloon-intro">
            <span>可在“搜索更多”中输入老师名字进行搜索，点击老师头像进入详情页可在右侧课表进行预约。</span>

        </section>
    </div>
    <div class="progress"><span class="indicator"></span></div>
        <div class="target" style="min-height:300px;"></div>
        <div class="buffer" style="display:none;"></div>

    </div><!--/teacher-->


    <div class="content curriculum" name="curriculum" style="display:none;">
    <div class="single single_style_position pt5">
        <section class="box balloon right-side-style r5 balloon-intro">
            <span>请选择每周几，课程内容，具体时间来进行搜索，且支持通过“关键字”输入外教名字搜索。</span>

        </section>
    </div>



<form name="searchbox_fixing_f" id="searchbox_fixing_f" action="/k/fixings/" method="post">

    <input type="hidden" name="sort_order">
    <input type="hidden" name="from_search" value="1" />
    <div class="palette" current-date="2025-07-28" current-time-span="1">
        <ul class="dates">
            <li class="prev"><a onclick="return false;"></a></li>
            <li class="selection">
                <ul class="inner" >
                    <li date="0" class=""><a href="#0"><strong></strong><em>星期日</em></a></li>
                    <li date="1" class=""><a href="#1"><strong></strong><em>星期一</em></a></li>
                    <li date="2" class=""><a href="#2"><strong></strong><em>星期二</em></a></li>
                    <li date="3" class=""><a href="#3"><strong></strong><em>星期三</em></a></li>
                    <li date="4" class=""><a href="#4"><strong></strong><em>星期四</em></a></li>
                    <li date="5" class=""><a href="#5"><strong></strong><em>星期五</em></a></li>
                    <li date="6" class=""><a href="#6"><strong></strong><em>星期六</em></a></li>
                </ul>
            </li>
            <li class="next"><a onclick="return false;"></a></li>
        </ul>
        <input type="hidden" name="date" value="2025-07-28">
    </div>
    <dl class="field top">
        <dt>老师</dt>
        <dd>
            <div class="form-radio" group="schedules_fixing_t" name="mode" value="all" label="展示全部"></div>
            <div class="form-radio" group="schedules_fixing_t" name="mode" value="bookmarked" label="我的收藏" ></div>
            <div class="form-radio" group="schedules_fixing_t" name="mode" value="taken" label="曾经预约" ></div>
            <input type="hidden" group="schedules_fixing_t" name="mode" value="all">
            <ul class="inline mt5">
                <li><div class="form-checkbtn" name="long_career" value="1" label="三年以上教龄"><input type="hidden" name="long_career"></div></li>
                <li><div class="form-checkbtn" name="business" value="1" label="商务英语"><input type="hidden" name="business"></div></li>
                <li><div class="form-checkbtn" name="kids" value="1" label="有亲和力"><input type="hidden" name="kids"></div></li>
                <li><div class="form-checkbtn" name="male" value="1" label="男性"><input type="hidden" name="male"></div></li>
                <li><div class="form-checkbtn" name="female" value="1" label="女性"><input type="hidden" name="female"></div></li>
            </ul>
        </dd>
    </dl>
    <dl class="field">
        <dt>课程（*必选）</dt>
        <dd style="position:relative">
            <div class="form-radio tip_click_one" group="schedules_fixing" name="lesson_time" value="30" label="25分钟"></div>
            <div class="form-radio tip_click_two" group="schedules_fixing" name="lesson_time" value="60" label="50分钟"></div>

            <input type="hidden" group="schedules_fixing" name="lesson_time" value="30">

            <div class="form-select ml10 w200" name="curriculum" input-keys="curriculum" placeholder="选择课程">
                <div style="display:none;">


                    <button value="1002672" lesson-time="30" label="青少儿主修课Smart Juniors ">青少儿主修课Smart Juniors

                    </button>



                    <button value="1002288" lesson-time="30" label="Good English典范英语(1-6)">Good English典范英语(1-6)

                    </button>



                    <button value="1002289" lesson-time="30" label="Good English 7 （典范英语 7）">Good English 7 （典范英语 7）

                    </button>



                    <button value="1002290" lesson-time="30" label="Good English 8 （典范英语 8）">Good English 8 （典范英语 8）

                    </button>



                    <button value="1002291" lesson-time="30" label="Good English 9 (典范英语 9)">Good English 9 (典范英语 9)

                    </button>



                    <button value="1002294" lesson-time="30" label="Write Source 英语写作">Write Source 英语写作

                        <span class="orange ml3">(+25pts)</span>
                    </button>



                    <button value="1002295" lesson-time="30" label="Time to Talk">Time to Talk

                    </button>



                    <button value="1002302" lesson-time="30" label="Basic English">Basic English

                    </button>



                    <button value="1002304" lesson-time="30" label="新主题对话">新主题对话

                    </button>



                    <button value="1002305" lesson-time="30" label="新主题对话(初级) New Topic Conversation for Beginners">新主题对话(初级) New Topic Conversation for Beginners

                    </button>



                    <button value="1002569" lesson-time="30" label="English Phonetics 音标课">English Phonetics 音标课

                    </button>



                    <button value="1002307" lesson-time="30" label="凯伦方法">凯伦方法

                    </button>



                    <button value="1002308" lesson-time="30" label="商务凯伦（限Stage8以上水平）">商务凯伦（限Stage8以上水平）

                    </button>



                    <button value="1002328" lesson-time="30" label="新版商务英语">新版商务英语

                    </button>



                    <button value="1002309" lesson-time="30" label="	商务凯伦（限Stage8以上水平）">	商务凯伦（限Stage8以上水平）

                    </button>



                    <button value="1002312" lesson-time="30" label="Global Business Skils">Global Business Skils

                    </button>



                    <button value="1002314" lesson-time="30" label="旅游英语">旅游英语

                    </button>



                    <button value="1002315" lesson-time="30" label="Welcome Aboard">Welcome Aboard

                    </button>



                    <button value="1002313" lesson-time="30" label="News Alert新闻英语">News Alert新闻英语

                    </button>



                    <button value="1002318" lesson-time="60" label="50分钟TOEIC强化 ">50分钟TOEIC强化

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002319" lesson-time="60" label="TOEIC Vocabulary">TOEIC Vocabulary

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002320" lesson-time="60" label="TOEIC Speaking">TOEIC Speaking

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002322" lesson-time="60" label="雅思写作 50 mins">雅思写作 50 mins

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002323" lesson-time="60" label="雅思口语30天">雅思口语30天

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002874" lesson-time="30" label="SDG Topics">SDG Topics

                    </button>



                    <button value="1002310" lesson-time="30" label="R.E.M.S.">R.E.M.S.

                    </button>



                    <button value="1002326" lesson-time="30" label="Beginner Writing">Beginner Writing

                        <span class="orange ml3">(+25pts)</span>
                    </button>



                    <button value="1002306" lesson-time="30" label="新日常生活英语">新日常生活英语

                    </button>



                    <button value="1002399" lesson-time="60" label="雅思听力 40 Days">雅思听力 40 Days

                    </button>



                    <button value="1002400" lesson-time="60" label="雅思阅读 (Academic) 40 Days">雅思阅读 (Academic) 40 Days

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002401" lesson-time="30" label="Wonder Words">Wonder Words

                    </button>



                    <button value="1002444" lesson-time="60" label="雅思写作 Writing Task 1 (Academic)">雅思写作 Writing Task 1 (Academic)

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002461" lesson-time="60" label="雅思写作 Writing Task 2">雅思写作 Writing Task 2

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002676" lesson-time="60" label="菲律宾游学说明会">菲律宾游学说明会

                        <span class="orange ml3">(+99999pts)</span>
                    </button>



                    <button value="1002880" lesson-time="60" label="IELTS Speaking MasterCourse">IELTS Speaking MasterCourse

                        <span class="orange ml3">(+50pts)</span>
                    </button>



                    <button value="1002894" lesson-time="60" label="IELTS Speaking Practice Test and Feedback">IELTS Speaking Practice Test and Feedback

                        <span class="orange ml3">(+50pts)</span>
                    </button>


                </div>
                <input type="hidden" name="curriculum">
            </div>

            <p class="error mt5" style="display:none;" error-keys="curriculum"></p>


<div class="single tip_content_one" style="position: absolute;top: 15px;left: 0px;display: none;">
    <section class="box  pl10 pr10 left-side-min-tip min_section_tip r5 balloon-intro">
        <span class="min_tip">标准课程，时间为25分钟一节课。</span>
    </section>
</div>

<div class="single tip_content_two" style="position: absolute;top: 15px;left: 0px;display: none">
    <section class="box pl10 pr10  left-side-min-tip min_section_tip r5 balloon-intro">
        <span class="min_tip">高阶专项课程或国际小班课，时间为50分钟一节课。</span>
    </section>
</div>
        </dd>
    </dl>
    <dl class="field fixing_content">
        <dt class="required">时间<br><span class="red f12px small">仅部分时间开放</span></dt>
        <dd>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="05:30-06:00" label="05:30"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="06:00-06:30" label="06:00"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="06:30-07:00" label="06:30"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="07:00-07:30" label="07:00"></div>

            <br>
            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="17:00-17:30" label="17:00"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="17:30-18:00" label="17:30"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="18:00-18:30" label="18:00"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="18:30-19:00" label="18:30"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="19:00-19:30" label="19:00"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="19:30-20:00" label="19:30"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="20:00-20:30" label="20:00"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="20:30-21:00" label="20:30"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="21:00-21:30" label="21:00"></div>


            <div class="form-radio" group="schedules_fixing_time_from_to" name="time_from_to" value="21:30-22:00" label="21:30"></div>

            <input type="hidden" group="schedules_fixing_time_from_to" name="time_from_to">
            <p class="error mt5" style="display:none;" error-keys="time_from_to"></p>

        </dd>
    </dl>
    <dl class="field">
        <dt>关键词</dt>
        <dd>
            <input type="text" name="keyword" class="w350" placeholder="关键词">
        </dd>
    </dl>
    <a name="results"></a>
    <ul class="btns">
        <li><a href="javascript:void(0)" class="btn to_fixing_page red fixing_btn w200" ><i class="icon-search mr5 f12px"></i>搜索</a></li>
    </ul>
</form>

    </div><!--/curriculums-->








</section><!--/searchbox-->


        </div>


        <div class="single pt30 mb30">


<section class="box calendarbox">
    <ul class="header">
        <li class="title"><i class="icon-calendar3 mr6"></i>您的课程</li>

        <li class="year-month"><span field="year-month" style="display:none;"><span field="year"></span>年 - <span field="month"></span>月</span></li>

        <li class="nav" btn-prev="1"><a href="#prev" class="prev"><i class="icon-arrow-left"></i></a></li>
        <li class="nav" btn-next="1"><a href="#next" class="next"><i class="icon-arrow-right"></i></a></li>
    </ul>

    <ul class="lessons" name="class_room_box"></ul>

    <div class="calendar">
        <div class="progress" style="display:none;"><span class="indicator"></span></div>
        <table>
            <thead>
            <tr>
                <th class="wday0">星期日</th>
                <th class="wday1">星期一</th>
                <th class="wday2">星期二</th>
                <th class="wday3">星期三</th>
                <th class="wday4">星期四</th>
                <th class="wday5">星期五</th>
                <th class="wday6">星期六</th>
            </tr>
            </thead>
            <tbody field="calendar">

            </tbody>
        </table>



    </div>
    <div class="tr p10">
        <a href="#list" base-href="/k/mypage/lessons/reserved/" class="btn blue xsmall w150" btn-list="1">列表展示全部预约</a>
    </div>
    <div class="buffer" style="display:none;"></div>

    <input type="hidden" name="message.cancel_success" value="您已成功取消预约">
</section>
<div class="mt10">
    <span class="mr5 vm"><i class="icon-pencil blue icon-pencil-color"></i>&nbsp;<span>已预约</span></span>
    <span class="mr5 vm"><i class="icon-star3 icon-star3-color"></i>&nbsp;<span>待评价</span></span>
    <span class="mr5 vm"><i class="icon-checkmark icon-checkmark-color"></i>&nbsp;<span>已评价</span></span>
</div>

        </div>



    </div><!--/left-->

    <div class="right">




<section class="pointsbox mb15">
    <h2 class="heading m10"><a href="/k/points/"><i class="icon-coin f17px mr4 top-1px"></i>课时点余额</a></h2>
    <div class="point"><strong>1,972</strong>pts</div>

    <p class="description">查看课时点记录
</p>


    <a href="/k/points/" class="btn small blue more">课时点记录</a>




</section>

<section class="latestlesson mb15">


    <h2 class="heading m10"><a href="/k/mypage/lessons/reserved/"><i class="icon-calendar3 mr6"></i>最近预约</a></h2>
    <dl>
        <dt>

            <a href="/k/teacher/48770340"><img src="/k/cache/images/teacher/8a/07/305d8611af6fc51457a8d0b7b1895d3f17a28a07.216x216.cut.png" alt="Gunther" class="thumb"></a>

        </dt>
        <dd>
            <div class="bold f14px blue vm mb2 nr"><i class="icon-clock mr3 top-1px"></i>2025-08-01</div>
            <div class="blue small mb10">18:00-18:30</div>

            <div><a href="/k/teacher/48770340" class="bold bluegray block mb3">Gunther<i class="icon-teacher gray ml4"></i></a></div>

        </dd>
    </dl>
    <a href="/k/mypage/lesson/55412520/request/" class="btn gray small more">给老师留言</a>
</section>




<section id="is_show_mentor_info" class="box informations mb15" style="display: none;">
    <h2 class="heading p10">联系卡</h2>
    <ul style="height: auto;overflow-y:hidden;">
        <li>我的班主任微信
            <!--span style="color: #ff7a42;" id="staff_name_info"></span-->
            <span style="font-size: 12px;cursor:pointer" id="show_wechat_qr_code">（二维码）</span>
            <div id="show_wechat_qr_code_info" style="display: none;">
                <img style="max-width:200px;width:100%;" id="wechat_code_img_info">
            </div>
        </li>
        <li id="is_show_staff_day_off_info" style="display: none;border-top: 0px;">
            <i class="icon-clock lightgray mr6"></i>休息时间：<span id="staff_day_off_info"></span>
        </li>
        <li style="border-top: 0px;"> <i class="icon-phone lightgray mr6"></i> ************</li>
        <li>
            <a href="https://adult.kuaikuenglish.com/k/support/" class="btn blue" style="width:48%" >意见反馈</a>
            <a id="menu_7moor" class="btn blue fill" style="width:48%;float:right;" target="_blank">在线客服</a>
        </li>
    </ul>
</section>

<section class="sidemenu mb15 ">
    <ul>

        <li><a href="http://www.kuaikuenglish.com/static/download/index.shtml" target="_blank">下载教材</a></li>




        <li><a href="/k/faq/">常见问题</a></li>
    </ul>
</section>

<div class="dialog confirm" id="dialog-fixing-warning" style="display:none; width:480px; height:310px;">
    <div class="header"><strong>固定须知</strong></div>
    <div class="content"><div class="inner">
            <div class="red">

                - 固定老师每课（25分钟）收取老师课时点的20%作为手续费；如果老师升级，手续费将随之调整。<br>
- 当老师有特殊情况不能上课时，当周的固定课程将被取消，不会产生课时点消耗。


            </div>
        </div></div>

    <div class="footer">
        <a href="#confirm" class="btn red w180 mr2" btn-confirm="1">同意</a>
        <a href="#cancel" class="btn gray w130 ml2" dialog-close="1">取消</a>
    </div>
    <a href="#close" class="close" dialog-close="1"></a>
</div>

<div class="dialog confirm" id="dialog-evaluate-result" style="display:none;width:480px; height:260px;">
    <div class="header">
        <strong>课程顾问满意度调查</strong>
    </div>
    <div class="content">
        <div class="inner">
            <div style="text-align: center;">感谢您的评价！</div>
        </div>
    </div>
    <div class="footer">
        <a class="btn gray w140" dialog-close="1">关闭</a>
    </div>
    <a href="" class="close" dialog-close="1"></a>
</div>

<script type="text/javascript">
window.onload = function(){
    var username = "Eren";
    var user_id = "259513078";
    var userUid = "BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54";
    var userId = parseInt(user_id);
    var paramJsonString = '%7B%22nickName%22%3A%22' + username + '_APP%22%7D';

    var accessId = "a15fddf0-3368-11e8-b09d-039b94366e02";//QQEnglish AFF 8000@kkyy

    var url = "https://webchat.7moor.com/wapchat.html?accessId=" + accessId + "&fromUrl=QQEng&urlTitle=PadApp&otherParams=" + paramJsonString + "&clientId=" + userUid;

    url = url + "&language=EN";

     document.getElementById("menu_7moor").href = url;
};
</script>



<div class="informations_content" >
    <input type="hidden" id="informations_site_id" value="113">
    <input type="hidden" id="informations_is_kid"  value="">
    <div style="display:none" name="informations_title">系统消息</div>
    <div id="informations_content" data="1">

    </div>
</div>




<section class=" mb15 learnRanking rankBox learnRanking_can_view_comparison" style="display: none">
    <input type="hidden" id="race_info_personal_data"  value="">
    <input type="hidden" id="race_info_site_data" value="">
    <input type="hidden" id="race_info_info_area_data" value="">
    <input type="hidden" id="student_area" value="">
    <input type="hidden" id="student_uid" value="BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54">
    <div style="display:none" name="classroom_total_good">累计获得表扬数</div>
    <div style="display:none" name="growth_footsteps1">总学习时长</div>
    <div style="display:none" name="growth_footsteps2">本月总学习时长</div>
    <div style="display:none" name="growth_footsteps_unit">min</div>
    <div style="display:none" name="growth_footsteps3">本月学习时长全球排名</div>
    <div class="hd">
        <ul>
            <li class="personal_title_li">成长足迹</li>
            <li class="comparison_title_li">月度平均学习时长</li>
        </ul>
    </div>
    <div class="bd">
        <ul class="lh">
            <li>
                <section class="learnRanking_can_view_personal relative"  style="display: none">
                    <div id="learnRanking_can_view_personal">

                    </div>
                    <div class="blue fr mt8" style="font-size: 17px;position: absolute;top: 0;right: 0;">

                        <a href="/k/faq/8"><i class="icon-help"></i></a>

                    </div>
                </section>
                <section class="learnRanking_can_view_personal_none_data relative" style="display: none">
                        no data
                </section>
            </li>
        </ul>
        <ul class="lh">
            <li>
                <div class="tab_wrapper tab-group tabStyle mt20 view_site_area_data" style="display: none">
                    <ul class="tab_list">
                        <li class="active"><a>National Rank</a></li>
                        <li><a>Regional Rank</a></li>
                    </ul>
                    <div class="content_wrapper">
                        <div class="tab_content active">
                            <div class="point country_content site_content_render" id="site_content_render_1">

                            </div>
                            <h2 class="moreData p10 pt20">
                                <a href="/k/campus/report/study_report/?target=site" class="btn red w90">More</a>
                            </h2>
                        </div>
                        <div class="tab_content">
                            <div class="point area_content area_content_text ml20 area_content_render" id="area_content_render_1">

                            </div>
                            <h2 class="moreData p10 pt20">
                                <a href="/k/campus/report/study_report/?target=area" class="btn red w90">More</a>
                            </h2>
                        </div>
                    </div>
                </div>

                <!--international-->
                <section class="tabStyle can_view_site_data" style="display: none;">
                    <div class="point country_content site_content_render" id="site_content_render_2">

                    </div>
                    <h2 class="moreData p10 pt20">
                        <a href="/k/campus/report/study_report/" class="btn red w90">More</a>
                    </h2>
                </section>

                <!-- prefecture-->
                <section class="tabStyle can_view_area_data" style="display: none;">
                    <div class="point area_content area_content_text ml20 area_content_render" id="area_content_render_2">

                    </div>
                    <h2 class="moreData p10 pt20">
                        <a href="/k/campus/report/study_report/" class="btn red w90">More</a>
                    </h2>
                </section>

            </li>
        </ul>
    </div>
</section>

<section class="mb15 learnRanking learnRanking_footprints_can_view_personal" style="display: none;">
    <h2 class="heading p10">
        <!-- <i class="icon-coin f17px mr4 top-1px"></i> -->成长足迹
    </h2>
    <div class="relative">
        <div id="learnRanking_footprints_can_view_personal">

        </div>
        <div class="blue fr mt8" style="font-size: 17px;position: absolute;top: 0;right: 0;">

            <a href="/k/faq/8"><i class="icon-help"></i></a>

        </div>
    </div>
</section>



<div banners-async="right" data="1" >
    <input type="hidden" name="banner_site_id" value="113">
    <input type="hidden" name="banner_is_kid" value="">
    <input type="hidden" name="banner_student_uid" value="BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54">
    <input type="hidden" name="banner_is_login" value="1">
    <div id="banners_async_right"></div>
</div>















    </div><!--/right-->

</div><!--/contents-->

<!--enter classroom error message-->
<div id="ajax_error_message" style="display:none">请求失败，请重试！</div>


<div class="dialog" id="notice_center_model" style="display:none; width:550px; height:300px;">
    <div class="content p10">
        <div class="inner tc">
            <i class="icon-bell top-2px mr3 notice_icon"></i>
            <div class="f14px bold mb10 notice_title tc p10 pt0"></div>
            <div class="mb25 notice_content break_word break_word2"></div>
        </div>
    </div>
    <div class="footer">
        <a href="" class="btn blue w100 ml2 notice_detail">详情</a>
        <a href="" class="btn gray w100 ml2 notice_close" dialog-close="1">忽略</a>
    </div>
    <a href="" class="close notice_close" dialog-close="1"></a>
</div>
<div class="dialog" id="notice_img_center_model" style="display:none; width:550px; height:300px;background: none;box-shadow: none">
    <a href="" class="notice_detail"><img id="notice_image" alt="" src="" style="max-width:100%;height:auto;"></a>
    <a href="" class="close notice_close" dialog-close="1"></a>
</div>


<!--enter classroom div -- hidden element start-->
<div style="display:none;">
    <a id="enter_classroom_common_tag_a" href="" target="_blank"></a>
</div>
<!--enter classroom div -- hidden element end-->

<div style="display:none">

    <div name="lesson_leave_text"><p class="mb5">确认不能上课、要向老师请假吗？</p>
<p class="mb5">老师可能未及时关注到您请假的消息，仍然会被记录旷课，旷课会额外扣除100%的课时；</p>
<p class="">但系统会将这部分课时在48小时内如数退还到您的账户，还请耐心等待。</p></div>

</div>
<input type="hidden" name="student_uid_content" value="BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54">
<input type="hidden" name="time_zone" value="Asia/Shanghai">
<div class="ai_curriculum_course_nodata_text" style="display: none">您还没有定制课程内容，<a href="/k/mypage/ai/curriculum/customized/#/">点击这里</a>开始定制吧！</div>
<input type="hidden" class="ai_curriculum_course_required_text" value="选择AI定制课">
<input type="hidden" class="ai_ticket_required_text" value="请选择一张次卡">
<!--footer-->

<!--app do not show footer-->
<footer>
    <div class="top">
        <div class="scroller"><i class="icon-footer-scroller-arrow-up"></i></div>


        <ul class="nav double divider">
            <li class="heading"><a href="https://adult.kuaikuenglish.com/k/mypage/">我的课程</a></li>
            <li><a href="https://adult.kuaikuenglish.com/k/searcher/schedules/">查看课表</a></li>

            <li><a href="https://adult.kuaikuenglish.com/k/points/">购买课时点</a></li>

            <li><a href="https://adult.kuaikuenglish.com/k/searcher/teachers/">查看老师</a></li>



            <li><a href="https://adult.kuaikuenglish.com/k/curriculums/">查看课程</a></li>

            <li><a target="_blank" href="http://www.kuaikuenglish.com/static/download/index.shtml" target="_blank">下载教材</a></li>

        </ul>
        <ul class="nav">
            <li class="heading"><a href="https://adult.kuaikuenglish.com/k/mypage/settings/">账户</a></li>
            <li><a href="https://adult.kuaikuenglish.com/k/mypage/settings/">账号管理</a></li>

            <li><a href="https://adult.kuaikuenglish.com/k/mypage/settings/family/">家庭账户</a></li>

            <li><a href="https://adult.kuaikuenglish.com/k/logout/" class="clear_token">退出</a></li>
        </ul>
        <ul class="nav divider">
            <li class="heading"><a href="https://adult.kuaikuenglish.com/k/faq/">帮助</a></li>
            <li><a href="https://adult.kuaikuenglish.com/k/faq/">常见问题</a></li>
            <li><a href="https://adult.kuaikuenglish.com/k/support/">意见反馈</a></li>
            <li>&nbsp;</li>
        </ul>


    </div>

<div class="bottom"><div class="inner">
    <ul class="nav">
        <li><a href="http://www.kuaikuenglish.com/static/about/index.shtml">关于我们</a></li>
        <li><a href="http://www.kuaikuenglish.com/static/private/index.shtml">隐私保护</a></li>
        <li><a href="http://www.kuaikuenglish.com/static/zhaopin/index.shtml">加入我们</a></li>
        <li><a href="http://www.kuaikuenglish.com/static/agreement/index.shtml">服务协议</a></li>
        <li><a href="http://coolstudy.com.cn/" target="_blank">快酷游学</a></li>
    </ul>
    <div class="copyright">Kuaikuenglish &copy; ALL RIGHTS RESERVED.</div>
    <div class="logo"><a href="/k/">快酷英语</a></div>
</div></div>


</footer>


<div id="message" style="display:none;"><div class="inner">
    <strong></strong>
    <a href="" class="close"><i class="icon-close"></i></a>
</div></div>





<script type="text/javascript" src="/k/static/0/js/lib/jquery-1.11.1.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.cookie.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.activity-indicator-1.0.0.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.mousewheel.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.jscrollpane.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/sprintf.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.ua.js"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.click-or-touch.js?v=4"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.progress.js?v=20230616"></script>
<script type="text/javascript" src="/k/static/0/js/lib/jquery.dialog.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/core.js?v=3"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/util.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/lang.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/message.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/form.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/error.js?v=4"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/popup.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/jquery.SuperSlide.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/common/jquery-tab.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/util.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/header.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/footer.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/voice.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/init.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/swiper.min.js"></script>
<script type="text/javascript"> var student_email = "<EMAIL>";</script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/lesson_tips.js"></script>
<script type="text/javascript" src="/k/static/135/js/lib/mithril.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/learn_ranking.js?v=20230409"></script>
<script type="text/javascript">$(function(){ $.qqe.uri.init({api_domain: "https://qqeapi.campustop.net/", http_domain: "adult.kuaikuenglish.com", https_domain: "adult.kuaikuenglish.com", prefix: "k", sns_prefix: "k"}); $.qqe.lang.init("zh"); });</script>
<div id="host_name" style="display: none">qqesite08</div>

<script type="text/javascript" src="/k/static/135/js/qqe/common/api.jwt.js?v=85"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/q_message_unread_count.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/kepler_reader_banner.js?v=5"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/informations_render.js?v=15"></script>
<script type="text/javascript">
    $(function () {
        $.qqe.site.tools.informations_render.init();
    })
</script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/learn_ranking_render.js?v=3"></script>
<script type="text/javascript">
    $(function () {
        var unit = {
            growth_footsteps_unit: "min"
        };
        $.qqe.site.tools.learn_ranking_render.prepare(unit);
    })
</script>



<script type="text/javascript">
    $().ready(function () {
        var url = "https://register.kuaikuenglish.com/adviser/adviser_mes/<EMAIL>";

        function show_evaluate_result() {
            var $dialog_evaluate_result = $("#dialog-evaluate-result");
            $.dialog.open($dialog_evaluate_result, {width: 480, height: 260});
        }

        function show_evaluate() {
            $.dialog.open($.qqe.uri.rel_base() + "/dialog/student/evaluate", {
                width: 480,
                height: 260
            }, function ($dialog_container) {
                var url = "https://register.kuaikuenglish.com/adviser/save_satisfaction";
                $("[btn-submit]", $dialog_container).clickOrTouch(function () {
                    $.post(url, {email: "<EMAIL>", evaluate_result: $("input[name='evaluate_result']").val()});
                    show_evaluate_result();
                });
            });
        }

        $.ajax({
            url: url,
            dataType: "script",
            cache: true,
            success: function () {
                if ("undefined" == typeof (adviser)) {
                    return;
                }
                if (adviser.name === "") {
                    return;
                }
                //$("#staff_name_info").text(adviser.name);
                if (adviser.day_off !== "") {
                    $("#staff_day_off_info").text(adviser.day_off);
                    $("#is_show_staff_day_off_info").show();
                }
                $("#wechat_code_img_info").attr("src", adviser.img_url);
                $("#is_show_mentor_info").show();
                $("#show_wechat_qr_code").hover(
                    function () {
                        $("#show_wechat_qr_code_info").show(200);
                    },
                    function () {
                        $("#show_wechat_qr_code_info").hide(200);
                    }
                );

                if (adviser.evaluate_flag === "1") {
                    show_evaluate();
                }

            }
        });
    });
</script>

<div style="position:fixed; width:40px; height:100px; right: 0px; bottom: 50%;margin-top:-50px;">
    <div id="chatBtn" class="chatBtn" style="background-color: rgb(0, 204, 205); border-radius: 5px 0px 0px 5px; right: -1px; bottom: 300px; padding: 10px 4px;">
        <a id="7moor" href="" target="_blank">
            <img width="32px" height="32px" src="//webchat.7moor.com/images/1.png?1221">
            <span style="color: white; display: block; padding: 6px 0px; text-align: center;">

                    在<br />线<br />客<br />服

            </span>
        </a>
    </div>
</div>
<script type="text/javascript">$(function(){
        var username = "Eren";
        var user_id = "259513078";
        var userUid = "BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54";
        var userId = parseInt(user_id);
        var paramJsonString = '%7B%22nickName%22%3A%22' + username + '_APP%22%7D';

        var accessId = "36c4aac0-0f32-11e8-be9a-e7749248af45";//kuaikuenglish cn 8000@kuaiku

        var url = "https://webchat.7moor.com/wapchat.html?accessId=" + accessId + "&fromUrl=QQEng&urlTitle=PadApp&otherParams=" + paramJsonString + "&clientId=" + userUid;

        //url = "http://baidu.com";
        $("#7moor").attr("href", url);
    });</script>



<script>
    var _hmt = _hmt || [];
    (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?ffab0c69280cc27115e22079b10deb5a";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
    })();
</script>


<script type="text/javascript">
    var error_nodata_text = $(".ai_curriculum_course_nodata_text").html();
    var student_uid = $("input[name='student_uid_content']").val();
    var key_ = isTargetLabel() ? "search_form_params"+student_uid : "submit_schedule_data"+student_uid;
    var jsonData = localStorage.getItem(key_) || "";
    var $form = $("#searchbox_schdule_f");
    var ai_curriculum_list_length = 0;
    function isTargetLabel() {
        return  true;

    }
</script>

<script type="text/javascript" src="/k/static/135/js/qqe/site/tools.lesson.js?v=21"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/searchbox.js?v=9"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/gold_animation.js?v=20200922"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/calendarbox.js?v=15"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/manage/bootstrap.min.js"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/q_message_notice_dialog.js?v=15"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/no_teacher_btn.js?v=20200730"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/lesson_leave_show.js?v=20230215"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/lesson_enter_classroom_show.js?v=20230215"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/render_search_schedules.js?v=103"></script>
<script type="text/javascript" src="/k/static/135/js/qqe/site/render_search_schedules_fixing_teacher.js?v=58"></script>

<script type="text/javascript">
    var student_uid = $("input[name='student_uid_content']").val();
    var key_ = "search_form_params" + student_uid;
    var jsonData = localStorage.getItem(key_) || "";
    if (jsonData) {
        var data = JSON.parse(jsonData);
        var $form = $("#searchbox_schdule_f");
        init_search_params($form,data)
    }
    var curriculum_error = {"key":"curriculum","message":"请您必须选择一个搜索的课程"};
    var time_from_to_error = {"key":"time_from_to","message":"请选择课程时间"};
    var use_enter_classroom_check = 0;
    var userId = 259513078;
    //use_enter_classroom_check
    $(function(){
        var lesson_leave_text = $("div[name='lesson_leave_text']").html();
        var lesson_leave_title = "我要请假";
        var lesson_leave_send_successful = "请假消息已发送";
        var disabled_text = "请您在课程开始前4分钟再次点击该按钮进入教室，感谢您的耐心等待。";
        <!--emoji text-->
        var review_perfect_words = "非常满意";
        var review_good_words = "比较满意";
        var review_okay_words = "一般";
        var review_terrible_words = "不满意";
        var review_unacceptable_words = "非常不满意"
        var tag_text = {
            one_star: "详细描述",
            three_star: "你希望老师在哪些方面能做得更好？",
            five_star: "请对本次课程老师进行评价",
        };
        $.qqe.site.searchbox.init();
        $.qqe.lesson_leave.init({
            lesson_leave_text: lesson_leave_text,
            lesson_leave_title:lesson_leave_title,
            lesson_leave_send_successful: lesson_leave_send_successful
        });
        $.qqe.site.calendarbox.init({
            lesson_leave_text: lesson_leave_text,
            lesson_leave_title:lesson_leave_title,
            lesson_leave_send_successful: lesson_leave_send_successful,
            review_perfect_words:review_perfect_words,
            review_good_words:review_good_words,
            review_okay_words:review_okay_words,
            review_terrible_words:review_terrible_words,
            review_unacceptable_words:review_unacceptable_words,
            tag_text: {
                one_star:tag_text.one_star,
                three_star:tag_text.three_star,
                five_star:tag_text.five_star
            }
        });
        $("#div_enter_classroom").show();
        $.qqe.report_teacher.init();
        $.qqe.lesson_enter_classroom.init({
            disabled_text:disabled_text,
        })

    });

    $(function(){
        $.qqe.search_schedules.result.listen_form_input();
        $.qqe.search_schedules_fixing_teacher.result.listen_form_input();
        $(".to_schedule_page").click(function (){
            window.location.href="/k/searcher/schedules/";
            localStorage.setItem('clickExecuted', 'false');
        })
        $(".to_fixing_page").click(function (){
            window.location.href="/k/searcher/fixings/";
            localStorage.setItem('clickExecuted', 'false');
        })
    })

    function init_search_params ($form,data){
        $("input[name='mode']",$form).val(data.mode);
        $("input[name='callan']",$form).val(data.callan);
        $("input[name='long_career']",$form).val(data.long_career);
        $("input[name='business']",$form).val(data.business);
        $("input[name='kids']",$form).val(data.kids);
        $("input[name='male']",$form).val(data.male);
        $("input[name='female']",$form).val(data.female);
        $("input[name='fixed']",$form).val(data.fixed);
        $("input[name='group']",$form).val(data.group);
        $("input[name='aigc_ticket']",$form).val(data.aigc_ticket);
        $("input[name='courseware']",$form).val(data.courseware);
        $('#select_courseware_form .item_list button').each(
            function(index, item){
                if ($(item).attr('value') === data.courseware) {
                    $('#select_courseware_form label').html($(item).text());
                    $('#select_courseware_form label').removeClass('placeholder');
                    $(item).addClass('selected');
                    $(item).attr('selected', true);
                }
            }
        );
        $("input[name='curriculum_type']",$form).val(data.curriculum_type);
        $("input[name='curriculum']",$form).val(data.curriculum);
        $("input[name='time_from_to']",$form).val(data.time_from_to);
        $("input[name='keyword']",$form).val(data.keyword);
        $("input[name='lesson_time']",$form).val(data.lesson_time);
        $("input[name='aigc_lesson_time']",$form).val(data.aigc_lesson_time);
    }
</script>

<script type="text/javascript" src="/k/static/135/js/qqe/site/searcher_curriculum_type.js?v=93"></script>

<div style="position:fixed; width:40px; height:100px; right: 0px; bottom: 50%;margin-top:-50px;">
    <div id="chatBtn" class="chatBtn" style="background-color: rgb(0, 204, 205); border-radius: 5px 0px 0px 5px; right: -1px; bottom: 300px; padding: 10px 4px;">
        <a id="7moor_url" href="" target="_blank">
            <img width="32px" height="32px" src="//webchat.7moor.com/images/1.png?1221">
            <span style="color: white; display: block; padding: 6px 0px; text-align: center;">

                    在<br />线<br />客<br />服

            </span>
        </a>
    </div>
</div>
<script type="text/javascript">$(function(){
    var username = "Eren";
    var user_id = "259513078";
    var userUid = "BBDF8C8E-58AA-11EF-BF0F-B2F1E92A6F54";
    var userId = parseInt(user_id);
    var paramJsonString = '%7B%22nickName%22%3A%22' + username + '_APP%22%7D';

    var accessId = "36c4aac0-0f32-11e8-be9a-e7749248af45";//kuaikuenglish cn 8000@kuaiku

    var url = "https://webchat.7moor.com/wapchat.html?accessId=" + accessId + "&fromUrl=QQEng&urlTitle=PadApp&otherParams=" + paramJsonString + "&clientId=" + userUid;

    //url = "http://baidu.com";
    $("#7moor_url").attr("href", url);
});</script>

</body>
</html>
