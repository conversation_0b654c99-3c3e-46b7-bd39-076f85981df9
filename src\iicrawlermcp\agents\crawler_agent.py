"""
Agent module for iICrawlerMCP.

This module provides functionality to build and configure LangChain agents
with browser automation tools.
"""

import logging
from typing import Optional, List
from langchain import hub
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import ChatOpenAI
from langchain_core.tools import BaseTool

from ..core.config import config
from ..tools.delegation_tools import get_delegation_tools
from .browser_agent import BrowserAgent

logger = logging.getLogger(__name__)


class CrawlerAgent:
    """
    A LangChain agent configured for web crawling tasks.
    
    This class encapsulates the creation and management of a LangChain agent
    that can perform web automation tasks using browser tools.
    """
    
    def __init__(
        self, 
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None
    ):
        """
        Initialize the CrawlerAgent.
        
        Args:
            tools: List of LangChain tools to use. If None, uses default tools.
            verbose: Whether to enable verbose logging. If None, uses config default.
            llm_config: Custom LLM configuration. If None, uses config defaults.
        """
        # 使用精选工具集，避免功能重复，强制使用智能委托
        if tools is None:
            # 精选浏览器工具 + 智能委托工具 = 无重复的完整工具集
            curated_browser_tools = self._get_curated_browser_tools()
            delegation_tools = get_delegation_tools()
            self.tools = curated_browser_tools + delegation_tools

            logger.info(f"CrawlerAgent initialized with curated tools:")
            logger.info(f"  - Browser tools: {len(curated_browser_tools)}")
            logger.info(f"  - Delegation tools: {len(delegation_tools)}")
            logger.info(f"  - Total tools: {len(self.tools)}")
        else:
            self.tools = tools
        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()
        
        self._llm = None
        self._agent = None
        self._executor = None
        self._browser_agent = None
        self._element_agent = None

    def _get_curated_browser_tools(self) -> List[BaseTool]:
        """
        获取精选的浏览器工具，避免与智能委托工具功能重复。

        基于行业最佳实践的工具选择策略：
        1. 只保留CrawlerAgent作为协调者必需的核心工具
        2. 移除与智能委托重复的工具，强制使用smart_element_finder
        3. 实现"Supervisor Pattern" - CrawlerAgent专注协调，不直接操作DOM

        保留的工具类别：
        - 导航工具：页面跳转和URL控制
        - 截图分析工具：页面状态获取和调试
        - 高级浏览器功能：JavaScript执行、等待、按键等

        移除的工具类别（通过智能委托使用）：
        - 元素交互工具：click_element, type_text, hover_element
        - DOM分析工具：所有dom_get_*系列工具

        Returns:
            精选的浏览器工具列表
        """
        from ..tools.browser_tools import (
            # 导航工具 - 协调者需要控制页面流转
            navigate_browser_advanced,

            # 截图和分析工具 - 用于页面状态获取和调试
            take_screenshot_advanced,
            analyze_screenshot,
            browser_snapshot,
            get_page_info,

            # 高级浏览器功能 - 复杂场景需要
            browser_wait_for,
            browser_evaluate,
            browser_press_key,
            browser_select_option
        )

        return [
            # 🌐 导航控制 - CrawlerAgent核心职责
            navigate_browser_advanced,

            # 📸 页面分析 - 状态获取和调试必需
            take_screenshot_advanced,
            analyze_screenshot,
            browser_snapshot,
            get_page_info,

            # ⚡ 高级功能 - 复杂场景支持
            browser_wait_for,
            browser_evaluate,
            browser_press_key,
            browser_select_option
        ]
    
    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create LLM: {e}")
                raise
        return self._llm
    
    def _create_agent(self) -> None:
        """Create the LangChain agent."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                prompt = hub.pull("hwchase17/openai-functions-agent")
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("Agent created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent: {e}")
                raise
    
    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=50,  # 增加最大迭代次数，支持复杂任务
                    handle_parsing_errors=True
                )
                logger.info("Agent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create agent executor: {e}")
                raise
        return self._executor
    
    def invoke(self, input_text: str) -> dict:
        """
        Execute a task using the agent.
        
        Args:
            input_text: The task description or instruction for the agent.
            
        Returns:
            A dictionary containing the agent's response and output.
        """
        executor = self._create_executor()
        
        try:
            logger.info(f"Executing task: {input_text}")
            result = executor.invoke({"input": input_text})
            logger.info("Task completed successfully")
            return result
        except Exception as e:
            logger.error(f"Task execution failed: {e}")
            raise
    
    def get_browser_agent(self) -> BrowserAgent:
        """
        Get or create a BrowserAgent for browser-specific operations.

        Returns:
            A BrowserAgent instance for browser automation tasks.
        """
        if self._browser_agent is None:
            self._browser_agent = BrowserAgent(
                verbose=self.verbose,
                llm_config=self.llm_config
            )
        return self._browser_agent

    def delegate_browser_task(self, task: str) -> dict:
        """
        Delegate a browser-specific task to the BrowserAgent.

        Args:
            task: Browser automation task description

        Returns:
            Result from the BrowserAgent
        """
        try:
            browser_agent = self.get_browser_agent()
            result = browser_agent.invoke(task)
            logger.info(f"Browser task delegated successfully: {task}")
            return result
        except Exception as e:
            logger.error(f"Browser task delegation failed: {e}")
            raise

    def get_element_agent(self):
        """
        Get or create an ElementAgent for element-specific operations.

        Returns:
            An ElementAgent instance for DOM element analysis and manipulation.
        """
        if self._element_agent is None:
            # Import here to avoid circular imports
            from .element_agent import ElementAgent
            self._element_agent = ElementAgent(
                verbose=self.verbose,
                llm_config=self.llm_config
            )
        return self._element_agent

    def delegate_element_task(self, task: str) -> dict:
        """
        Delegate an element-specific task to the ElementAgent.

        Args:
            task: Element analysis or manipulation task description

        Returns:
            Result from the ElementAgent
        """
        try:
            element_agent = self.get_element_agent()
            result = element_agent.invoke(task)
            logger.info(f"Element task delegated successfully: {task}")
            return result
        except Exception as e:
            logger.error(f"Element task delegation failed: {e}")
            raise

    def cleanup(self) -> None:
        """Clean up resources used by the agent."""
        try:
            # Clean up browser agent first
            if self._browser_agent:
                self._browser_agent.cleanup()
                self._browser_agent = None

            # Clean up element agent
            if self._element_agent:
                self._element_agent.cleanup()
                self._element_agent = None

            # Clean up general tools
            from ..tools.browser_tools import cleanup_tools
            cleanup_tools()
            logger.info("Agent cleanup completed")
        except Exception as e:
            logger.error(f"Error during agent cleanup: {e}")


def build_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None
) -> CrawlerAgent:
    """
    Build and return a configured CrawlerAgent instance.
    
    Args:
        tools: List of LangChain tools to use. If None, uses default tools.
        verbose: Whether to enable verbose logging. If None, uses config default.
        llm_config: Custom LLM configuration. If None, uses config defaults.
        
    Returns:
        A configured CrawlerAgent instance.
        
    Example:
        agent = build_agent()
        result = agent.invoke("Navigate to https://google.com and take a screenshot")
    """
    # Validate configuration before creating agent
    config.validate()
    
    try:
        agent = CrawlerAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("Agent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build agent: {e}")
        raise


def create_simple_agent() -> AgentExecutor:
    """
    Create a simple AgentExecutor for backward compatibility.
    
    Returns:
        An AgentExecutor instance configured with default settings.
        
    Note:
        This function is provided for backward compatibility.
        Consider using build_agent() for new code.
    """
    agent = build_agent()
    return agent._create_executor()
