# 🔌 MCP接口快速开始指南

## 📋 概述

iICrawlerMCP现在支持MCP (Model Context Protocol) 接口，允许任何支持MCP的客户端通过标准协议访问智能网页自动化功能。

## 🚀 快速开始

### 1. 安装依赖

```bash
# 安装MCP依赖
pip install mcp

# 或者安装完整项目依赖
pip install -r requirements.txt
```

### 2. 设置环境变量

```bash
export OPENAI_API_KEY="your-openai-api-key"
export OPENAI_MODEL="gpt-3.5-turbo"  # 可选
export HEADLESS="true"               # 可选
```

### 3. 启动MCP服务器

```bash
# 方法1: 直接运行脚本
python scripts/start_mcp_server.py

# 方法2: 使用安装的命令 (需要先 pip install -e .)
iicrawlermcp-mcp

# 检查配置
python scripts/start_mcp_server.py --check-config
```

### 4. 使用MCP客户端

```python
import asyncio
from mcp import ClientSession
from mcp.client.stdio import stdio_client

async def demo():
    # 连接到MCP服务器
    async with stdio_client() as (read, write):
        async with ClientSession(read, write) as session:
            await session.initialize()
            
            # 列出可用工具
            tools = await session.list_tools()
            print(f"可用工具: {[t.name for t in tools.tools]}")
            
            # 执行智能网页任务
            result = await session.call_tool(
                "intelligent_web_task",
                {
                    "task_description": "打开google.com并搜索Python教程"
                }
            )
            print(f"任务结果: {result.content[0].text}")

if __name__ == "__main__":
    asyncio.run(demo())
```

## 🔧 可用工具

### intelligent_web_task
**统一的智能网页任务接口**

- **描述**: 执行任何网页相关任务的统一入口
- **参数**: 
  - `task_description` (string): 自然语言描述的任务
- **示例**:
  ```json
  {
    "task_description": "打开淘宝，搜索iPhone 15，找到价格最低的商品并截图"
  }
  ```

### browser_status
**浏览器状态查询**

- **描述**: 获取当前浏览器状态信息
- **参数**: 无
- **返回**: 当前URL、页面标题等信息

### take_screenshot
**快速截图工具**

- **描述**: 保存当前页面截图
- **参数**:
  - `filename` (string, 可选): 截图文件名，默认"screenshot.png"
- **示例**:
  ```json
  {
    "filename": "my_screenshot.png"
  }
  ```

### cleanup_browser
**资源清理**

- **描述**: 清理浏览器资源，释放内存
- **参数**: 无

## 📝 使用示例

### 基础网页操作
```python
# 导航和截图
await session.call_tool(
    "intelligent_web_task",
    {"task_description": "打开google.com并截图"}
)

# 搜索操作
await session.call_tool(
    "intelligent_web_task", 
    {"task_description": "在google搜索'Python教程'并点击第一个结果"}
)
```

### 复杂任务
```python
# 电商搜索
await session.call_tool(
    "intelligent_web_task",
    {"task_description": "打开淘宝，搜索iPhone 15，找到价格最低的商品信息"}
)

# 表单填写
await session.call_tool(
    "intelligent_web_task",
    {"task_description": "打开httpbin.org/forms/post，填写表单并提交"}
)
```

### 状态管理
```python
# 检查浏览器状态
status = await session.call_tool("browser_status", {})
print(status.content[0].text)

# 清理资源
await session.call_tool("cleanup_browser", {})
```

## 🔍 故障排除

### 常见问题

1. **服务器启动失败**
   ```bash
   # 检查环境变量
   python scripts/start_mcp_server.py --check-config
   
   # 检查依赖
   pip install -r requirements.txt
   ```

2. **客户端连接失败**
   - 确保服务器正在运行
   - 检查stdio连接是否正确建立

3. **任务执行失败**
   - 检查OPENAI_API_KEY是否设置
   - 查看服务器日志输出

### 调试技巧

1. **启用详细日志**
   ```bash
   export VERBOSE=true
   python scripts/start_mcp_server.py
   ```

2. **使用演示脚本**
   ```bash
   python scripts/mcp_client_demo.py
   ```

## 🌟 最佳实践

1. **任务描述要清晰具体**
   - ✅ "打开google.com，搜索'Python教程'，点击第一个结果"
   - ❌ "搜索Python"

2. **合理使用资源清理**
   - 长时间运行后调用`cleanup_browser`
   - 避免浏览器实例累积

3. **错误处理**
   - 检查工具调用的返回结果
   - 处理网络超时和页面加载失败

4. **性能优化**
   - 复用浏览器会话
   - 避免频繁的页面跳转

## 📚 更多资源

- [MCP协议文档](https://modelcontextprotocol.io/)
- [iICrawlerMCP完整文档](../README.md)
- [Agent架构说明](../architecture/agents.md)
- [工具参考](../api/tools.md)
