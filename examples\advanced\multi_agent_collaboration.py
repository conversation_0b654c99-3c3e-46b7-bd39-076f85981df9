#!/usr/bin/env python3
"""
Multi-Agent Collaboration Example

This example demonstrates how CrawlerAgent, BrowserAgent, and ElementAgent
work together to accomplish complex web automation tasks.
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent, build_browser_agent, build_element_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def demonstrate_individual_agents():
    """Demonstrate each agent working individually."""
    
    print("\n🔧 Individual Agent Demonstrations")
    print("-" * 40)
    
    # 1. BrowserAgent - Basic browser control
    print("\n1️⃣ BrowserAgent - Browser Control")
    browser_agent = build_browser_agent()
    result = browser_agent.invoke("Navigate to https://httpbin.org/html")
    print(f"BrowserAgent: {result['output'][:100]}...")
    
    # 2. ElementAgent - DOM analysis
    print("\n2️⃣ ElementAgent - DOM Analysis")
    element_agent = build_element_agent()
    result = element_agent.invoke("Analyze the page structure and find all interactive elements")
    print(f"ElementAgent: {result['output'][:100]}...")
    
    # 3. CrawlerAgent - Coordination
    print("\n3️⃣ CrawlerAgent - Task Coordination")
    crawler_agent = build_agent()
    result = crawler_agent.invoke("Take a screenshot of the current page")
    print(f"CrawlerAgent: {result['output'][:100]}...")
    
    # Cleanup
    browser_agent.cleanup()
    element_agent.cleanup()
    crawler_agent.cleanup()


def demonstrate_agent_delegation():
    """Demonstrate CrawlerAgent delegating tasks to specialized agents."""
    
    print("\n🤝 Agent Delegation Demonstration")
    print("-" * 40)
    
    crawler_agent = build_agent()
    
    try:
        # Navigate using browser delegation
        print("\n📍 Delegating navigation to BrowserAgent...")
        result = crawler_agent.delegate_browser_task("Navigate to https://httpbin.org/forms/post")
        print(f"Browser delegation result: {result['output'][:100]}...")
        
        # Analyze elements using element delegation
        print("\n🔍 Delegating element analysis to ElementAgent...")
        result = crawler_agent.delegate_element_task("Find all form fields and describe their types")
        print(f"Element delegation result: {result['output'][:100]}...")
        
        # Take screenshot using browser delegation
        print("\n📸 Delegating screenshot to BrowserAgent...")
        result = crawler_agent.delegate_browser_task("Take a screenshot of the form")
        print(f"Screenshot delegation result: {result['output'][:100]}...")
        
    finally:
        crawler_agent.cleanup()


def demonstrate_complex_workflow():
    """Demonstrate a complex workflow using multiple agents."""
    
    print("\n🎯 Complex Multi-Agent Workflow")
    print("-" * 40)
    
    crawler_agent = build_agent()
    
    try:
        # Step 1: Navigate and analyze
        print("\n1️⃣ Navigate and analyze page structure...")
        result = crawler_agent.invoke("""
            Navigate to https://httpbin.org/forms/post and then analyze the page to find:
            1. All form fields
            2. The submit button
            3. Any validation requirements
        """)
        print(f"Analysis complete: {result['output'][:150]}...")
        
        # Step 2: Intelligent form interaction
        print("\n2️⃣ Intelligent form filling...")
        result = crawler_agent.invoke("""
            Fill out the form with test data:
            - Find the customer name field and enter 'John Doe'
            - Find the telephone field and enter '555-1234'
            - Find the email field and enter '<EMAIL>'
            - Find any text area and enter 'This is a test message'
        """)
        print(f"Form filling result: {result['output'][:150]}...")
        
        # Step 3: Final screenshot
        print("\n3️⃣ Taking final screenshot...")
        result = crawler_agent.invoke("Take a screenshot showing the filled form")
        print(f"Screenshot result: {result['output'][:100]}...")
        
    finally:
        crawler_agent.cleanup()


def main():
    """Run all multi-agent collaboration demonstrations."""
    
    print("🤖 Multi-Agent Collaboration Demo")
    print("=" * 50)
    
    try:
        # Validate configuration
        config.validate()
        print("✅ Configuration validated")
        
        # Run demonstrations
        demonstrate_individual_agents()
        demonstrate_agent_delegation()
        demonstrate_complex_workflow()
        
        print("\n✅ Multi-agent collaboration demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.error(f"Multi-agent demo error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
