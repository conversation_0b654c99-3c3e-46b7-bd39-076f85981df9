"""
iICrawlerMCP的MCP服务器实现

提供标准MCP协议接口，允许外部客户端通过MCP协议访问
iICrawlerMCP的智能网页自动化功能。
"""

import asyncio
import logging
import sys
from typing import Any, Sequence

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    ListToolsResult,
    Tool,
    TextContent,
)

from .schemas import TOOL_SCHEMAS
from .tools import execute_tool

logger = logging.getLogger(__name__)


class MCPServer:
    """
    iICrawlerMCP的MCP服务器实现
    
    提供以下工具：
    - intelligent_web_task: 智能网页任务统一入口
    - browser_status: 浏览器状态查询
    - take_screenshot: 快速截图
    - cleanup_browser: 资源清理
    """
    
    def __init__(self, server_name: str = "iicrawlermcp"):
        """
        初始化MCP服务器
        
        Args:
            server_name: 服务器名称
        """
        self.server = Server(server_name)
        self.server_name = server_name
        self._setup_handlers()
        
        logger.info(f"🚀 MCP服务器初始化完成: {server_name}")
    
    def _setup_handlers(self):
        """设置MCP协议处理器"""
        
        @self.server.list_tools()
        async def list_tools() -> list[Tool]:
            """列出所有可用工具"""
            tools = [
                Tool(
                    name="intelligent_web_task",
                    description="执行智能网页任务的统一入口，支持导航、搜索、点击、截图等所有网页操作",
                    inputSchema=TOOL_SCHEMAS["intelligent_web_task"]
                ),
                Tool(
                    name="browser_status", 
                    description="获取当前浏览器状态，包括URL、标题等信息",
                    inputSchema=TOOL_SCHEMAS["browser_status"]
                ),
                Tool(
                    name="take_screenshot",
                    description="快速截图工具，保存当前页面截图",
                    inputSchema=TOOL_SCHEMAS["take_screenshot"]
                ),
                Tool(
                    name="cleanup_browser",
                    description="清理浏览器资源，释放内存",
                    inputSchema=TOOL_SCHEMAS["cleanup_browser"]
                )
            ]
            
            logger.info(f"📋 列出工具: {[t.name for t in tools]}")
            return tools
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: dict) -> list[TextContent]:
            """执行工具调用"""
            try:
                logger.info(f"🔧 调用工具: {name}, 参数: {arguments}")
                
                # 执行工具
                result = await execute_tool(name, arguments)
                
                logger.info(f"✅ 工具执行完成: {name}")
                return [TextContent(type="text", text=result)]
                
            except ValueError as e:
                # 工具不存在
                error_msg = f"❌ 工具错误: {str(e)}"
                logger.error(error_msg)
                return [TextContent(type="text", text=error_msg)]
                
            except Exception as e:
                # 其他执行错误
                error_msg = f"❌ 工具执行失败: {str(e)}"
                logger.error(error_msg)
                return [TextContent(type="text", text=error_msg)]
    
    async def run(self):
        """运行MCP服务器"""
        logger.info(f"🌐 启动MCP服务器: {self.server_name}")
        
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name=self.server_name,
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    )
                )
            )


async def start_mcp_server(server_name: str = "iicrawlermcp") -> None:
    """
    启动MCP服务器的便捷函数
    
    Args:
        server_name: 服务器名称
    """
    try:
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            stream=sys.stderr  # MCP使用stdio，所以日志输出到stderr
        )
        
        # 验证配置
        from ..core.config import config
        config.validate()
        
        # 创建并运行服务器
        server = MCPServer(server_name)
        await server.run()
        
    except KeyboardInterrupt:
        logger.info("👋 MCP服务器已停止")
    except Exception as e:
        logger.error(f"❌ MCP服务器启动失败: {e}")
        raise


def main():
    """主函数入口"""
    print("🚀 启动iICrawlerMCP服务器...", file=sys.stderr)
    print("📡 MCP协议: stdio", file=sys.stderr)
    print("🤖 主Agent: CrawlerAgent", file=sys.stderr)
    print("🔧 可用工具: intelligent_web_task, browser_status, take_screenshot, cleanup_browser", file=sys.stderr)
    print("-" * 50, file=sys.stderr)
    
    try:
        asyncio.run(start_mcp_server())
    except KeyboardInterrupt:
        print("\n👋 服务器已停止", file=sys.stderr)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
