#!/usr/bin/env python3
"""
端到端多智能体协作演示

这个演示脚本展示了iICrawlerMCP项目中多智能体协作的完整工作流程，
包括消息规范化、agent通讯机制和结构化输出等核心特性。

演示场景：
1. 智能网页表单填写和提交
2. 多智能体协作完成复杂任务
3. 结构化消息传递验证
4. 错误处理和恢复机制
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent, build_browser_agent, build_element_agent
from iicrawlermcp.core.config import config
from iicrawlermcp.core.schemas import AgentRequest, AgentResponse, SimpleExecutionResult
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class E2EMultiAgentDemo:
    """端到端多智能体协作演示类"""
    
    def __init__(self):
        """初始化演示环境"""
        self.crawler_agent = None
        self.browser_agent = None
        self.element_agent = None
        self.demo_results = {}
        
    def setup(self):
        """设置演示环境"""
        print("🚀 初始化多智能体协作演示环境")
        print("=" * 50)
        
        try:
            # 验证配置
            config.validate()
            print("✅ 配置验证通过")
            
            # 创建智能体实例
            self.crawler_agent = build_agent()
            self.browser_agent = build_browser_agent()
            self.element_agent = build_element_agent()
            
            print("✅ 所有智能体创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 清理演示资源...")
        
        agents = [self.crawler_agent, self.browser_agent, self.element_agent]
        for agent in agents:
            if agent:
                try:
                    agent.cleanup()
                except Exception as e:
                    logger.warning(f"清理警告: {e}")
        
        print("✅ 资源清理完成")
    
    def demo_01_individual_agents(self):
        """演示1: 各个智能体的独立功能"""
        print("\n🤖 演示1: 各智能体独立功能")
        print("-" * 30)
        
        try:
            # BrowserAgent - 浏览器控制
            print("1️⃣ BrowserAgent - 浏览器控制")
            result = self.browser_agent.invoke("Navigate to https://httpbin.org/html")
            print(f"   结果: {result['output'][:80]}...")
            
            # ElementAgent - DOM分析
            print("2️⃣ ElementAgent - DOM元素分析")
            result = self.element_agent.invoke("Analyze the page structure and find interactive elements")
            print(f"   结果: {result['output'][:80]}...")
            
            # CrawlerAgent - 任务协调
            print("3️⃣ CrawlerAgent - 任务协调")
            result = self.crawler_agent.invoke("Take a screenshot of the current page")
            print(f"   结果: {result['output'][:80]}...")
            
            self.demo_results["individual_agents"] = "SUCCESS"
            print("✅ 各智能体独立功能演示完成")
            
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            self.demo_results["individual_agents"] = f"FAILED: {e}"
    
    def demo_02_delegation_mechanism(self):
        """演示2: 智能体委托机制"""
        print("\n🤝 演示2: 智能体委托机制")
        print("-" * 30)
        
        try:
            # 浏览器任务委托
            print("1️⃣ 委托浏览器任务")
            task = "Navigate to https://httpbin.org/forms/post and take a screenshot"
            result = self.crawler_agent.delegate_browser_task(task)
            print(f"   浏览器委托结果: {result['output'][:80]}...")
            
            # 元素分析任务委托
            print("2️⃣ 委托元素分析任务")
            task = "Find all form fields and describe their types and properties"
            result = self.crawler_agent.delegate_element_task(task)
            print(f"   元素委托结果: {result['output'][:80]}...")
            
            self.demo_results["delegation"] = "SUCCESS"
            print("✅ 委托机制演示完成")
            
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            self.demo_results["delegation"] = f"FAILED: {e}"
    
    def demo_03_structured_communication(self):
        """演示3: 结构化通信协议"""
        print("\n📨 演示3: 结构化通信协议")
        print("-" * 30)
        
        try:
            # 创建标准化请求
            request = AgentRequest(
                agent_type="browser",
                input_data={"url": "https://httpbin.org/html"},
                expected_output_schema="SimpleExecutionResult",
                metadata={"demo": "structured_communication", "priority": "high"}
            )
            
            print(f"1️⃣ 创建标准化请求:")
            print(f"   请求ID: {request.request_id}")
            print(f"   智能体类型: {request.agent_type}")
            print(f"   时间戳: {request.timestamp}")
            print(f"   元数据: {request.metadata}")
            
            # 模拟处理和响应
            start_time = time.time()
            
            # 实际执行任务（通过浏览器智能体）
            result = self.browser_agent.invoke("Navigate to https://httpbin.org/html")
            
            processing_time = time.time() - start_time
            
            # 创建标准化响应
            response = AgentResponse(
                request_id=request.request_id,
                status="success",
                data={"output": result.get("output", ""), "success": True},
                processing_time_seconds=processing_time
            )
            
            print(f"2️⃣ 生成标准化响应:")
            print(f"   响应状态: {response.status}")
            print(f"   处理时间: {response.processing_time_seconds:.2f}秒")
            print(f"   数据长度: {len(str(response.data))}字符")
            
            self.demo_results["structured_communication"] = "SUCCESS"
            print("✅ 结构化通信协议演示完成")
            
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            self.demo_results["structured_communication"] = f"FAILED: {e}"
    
    def demo_04_complex_workflow(self):
        """演示4: 复杂多智能体工作流"""
        print("\n🎯 演示4: 复杂多智能体工作流")
        print("-" * 30)
        
        try:
            # 定义复杂的多步骤任务
            complex_task = """
            请完成以下复杂的网页自动化任务：
            
            1. 导航到 https://httpbin.org/forms/post
            2. 分析页面结构，识别所有表单元素
            3. 智能填写表单：
               - 客户姓名字段填入 'John Doe'
               - 电话字段填入 '555-0123'
               - 邮箱字段填入 '<EMAIL>'
               - 评论区域填入 'This is an automated test message'
            4. 截图显示填写后的表单
            5. 提供任务完成的详细报告
            """
            
            print("🎯 执行复杂任务...")
            print("任务描述:", complex_task.strip())
            
            start_time = time.time()
            result = self.crawler_agent.invoke(complex_task)
            execution_time = time.time() - start_time
            
            print(f"\n📊 任务执行结果:")
            print(f"   执行时间: {execution_time:.2f}秒")
            print(f"   结果摘要: {result['output'][:150]}...")
            
            # 验证任务是否成功
            if "error" not in result['output'].lower() and execution_time < 180:
                self.demo_results["complex_workflow"] = "SUCCESS"
                print("✅ 复杂工作流演示完成")
            else:
                self.demo_results["complex_workflow"] = "PARTIAL_SUCCESS"
                print("⚠️ 复杂工作流部分完成")
                
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            self.demo_results["complex_workflow"] = f"FAILED: {e}"
    
    def demo_05_error_handling(self):
        """演示5: 错误处理和恢复机制"""
        print("\n⚠️ 演示5: 错误处理和恢复机制")
        print("-" * 30)
        
        try:
            # 测试无效URL的错误处理
            print("1️⃣ 测试无效URL处理")
            invalid_task = "Navigate to https://this-is-an-invalid-url-12345.com"
            result = self.crawler_agent.invoke(invalid_task)
            print(f"   错误处理结果: {result['output'][:80]}...")
            
            # 测试无效元素查找的错误处理
            print("2️⃣ 测试无效元素查找处理")
            # 先导航到有效页面
            self.crawler_agent.invoke("Navigate to https://httpbin.org/html")
            # 然后尝试查找不存在的元素
            invalid_element_task = "Find an element with id 'non-existent-element-12345'"
            result = self.crawler_agent.invoke(invalid_element_task)
            print(f"   元素查找结果: {result['output'][:80]}...")
            
            self.demo_results["error_handling"] = "SUCCESS"
            print("✅ 错误处理机制演示完成")
            
        except Exception as e:
            print(f"❌ 演示失败: {e}")
            self.demo_results["error_handling"] = f"FAILED: {e}"
    
    def generate_report(self):
        """生成演示报告"""
        print("\n📊 生成演示报告")
        print("=" * 50)
        
        total_demos = len(self.demo_results)
        successful_demos = sum(1 for result in self.demo_results.values() if result == "SUCCESS")
        
        print(f"📈 演示统计:")
        print(f"   总演示数: {total_demos}")
        print(f"   成功演示: {successful_demos}")
        print(f"   成功率: {(successful_demos/total_demos)*100:.1f}%")
        
        print(f"\n📋 详细结果:")
        for demo_name, result in self.demo_results.items():
            status_icon = "✅" if result == "SUCCESS" else "⚠️" if "PARTIAL" in result else "❌"
            print(f"   {status_icon} {demo_name}: {result}")
        
        # 生成JSON报告
        report = {
            "timestamp": datetime.now().isoformat(),
            "total_demos": total_demos,
            "successful_demos": successful_demos,
            "success_rate": (successful_demos/total_demos)*100,
            "results": self.demo_results,
            "summary": "Multi-agent collaboration system demonstration completed"
        }
        
        try:
            report_path = "screenshots/e2e_demo_report.json"
            os.makedirs(os.path.dirname(report_path), exist_ok=True)
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"📄 详细报告已保存到: {report_path}")
        except Exception as e:
            print(f"⚠️ 报告保存失败: {e}")
        
        return successful_demos == total_demos
    
    def run_full_demo(self):
        """运行完整演示"""
        print("🎭 iICrawlerMCP 端到端多智能体协作演示")
        print("=" * 60)
        print("本演示将展示多智能体协作、消息规范化和通讯机制的完整工作流程")
        print()
        
        if not self.setup():
            return False
        
        try:
            # 运行所有演示
            self.demo_01_individual_agents()
            self.demo_02_delegation_mechanism()
            self.demo_03_structured_communication()
            self.demo_04_complex_workflow()
            self.demo_05_error_handling()
            
            # 生成报告
            success = self.generate_report()
            
            if success:
                print("\n🎉 所有演示成功完成！")
                print("💡 多智能体协作系统运行正常，结构改进验证通过。")
            else:
                print("\n⚠️ 部分演示未完全成功，请检查系统配置。")
            
            return success
            
        finally:
            self.cleanup()


def main():
    """主函数"""
    demo = E2EMultiAgentDemo()
    success = demo.run_full_demo()
    return 0 if success else 1


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
