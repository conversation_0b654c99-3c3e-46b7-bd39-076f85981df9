#!/usr/bin/env python3
"""
维基百科迪士尼信息研究示例

这个示例演示了如何使用CrawlerAgent自动完成复杂的多步骤任务：
1. 导航到维基百科
2. 搜索迪士尼乐园
3. 提取相关信息
4. 截图保存结果

使用最简单的方法：让CrawlerAgent自动选择合适的Agent和工具。
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def wikipedia_disney_research():
    """
    使用CrawlerAgent自动研究维基百科上的迪士尼信息
    
    这个函数展示了如何用一个简单的任务描述，
    让CrawlerAgent自动选择合适的工具和Agent来完成复杂任务。
    """
    
    print("📚 维基百科迪士尼信息自动研究")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent - 这是我们唯一需要的Agent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 定义完整的任务 - 让AI自动选择如何执行
        task_description = """
        我需要从维基百科研究迪士尼乐园的信息，请按以下步骤自动执行：
        
        1. 导航到 https://zh.wikipedia.org/
        2. 在搜索框中搜索"迪士尼乐园"
        3. 点击搜索结果进入迪士尼乐园的页面
        4. 提取页面中的关键信息，包括：
           - 迪士尼乐园的基本介绍
           - 开园时间和地点信息
           - 主要景点或特色
        5. 截取页面截图作为记录
        
        请在执行过程中提供详细的步骤反馈，如果某个步骤遇到困难，请尝试其他方法。
        最终请整理并返回找到的迪士尼乐园信息。
        """
        
        print("\n🚀 开始执行自动化任务...")
        print("📋 任务描述：研究维基百科上的迪士尼乐园信息")
        print("🎯 让CrawlerAgent自动选择最佳的执行策略...")
        
        # 执行任务 - CrawlerAgent会自动选择使用BrowserAgent还是ElementAgent
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 任务执行完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 任务执行失败: {e}")
        logger.error(f"Wikipedia Disney research error: {e}")
        return None


def simple_navigation_test():
    """
    简单的导航测试，避免复杂的搜索操作
    """
    
    print("🌐 简单导航测试")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 简单的导航和截图任务
        task_description = """
        请执行以下简单任务：
        
        1. 导航到 https://zh.wikipedia.org/wiki/迪士尼乐园
        2. 等待页面完全加载
        3. 截取页面的完整截图
        4. 提取页面标题和第一段的内容
        
        这是一个简单的测试任务，主要验证导航和信息提取功能。
        """
        
        print("\n🚀 开始执行简单导航测试...")
        print("📋 任务：直接访问迪士尼乐园维基页面")
        
        # 执行任务
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 导航测试完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 导航测试失败: {e}")
        logger.error(f"Simple navigation test error: {e}")
        return None


def main():
    """主函数"""
    
    print("📚 维基百科迪士尼信息研究演示")
    print("=" * 60)
    print("这个演示将展示CrawlerAgent如何自动：")
    print("• 🌐 导航到维基百科")
    print("• 🔍 搜索迪士尼乐园信息")
    print("• 📄 提取页面关键信息")
    print("• 📸 截图保存结果")
    print("• 🤖 全程AI自动选择最佳策略")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 完整搜索测试（包含搜索操作）")
    print("2. 简单导航测试（直接访问页面）")
    
    choice = input("\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n🔍 执行完整搜索测试...")
        result = wikipedia_disney_research()
    elif choice == "2":
        print("\n🌐 执行简单导航测试...")
        result = simple_navigation_test()
    else:
        print("👋 无效选择，执行简单导航测试...")
        result = simple_navigation_test()
    
    if result:
        print("\n🎉 维基百科迪士尼信息研究完成！")
        print("\n💡 关键特点：")
        print("• ✅ 单一Agent调用 - 只需要创建一个CrawlerAgent")
        print("• ✅ 自然语言任务描述 - 无需编程技能")
        print("• ✅ 自动工具选择 - AI自动选择BrowserAgent或ElementAgent")
        print("• ✅ 智能错误恢复 - 遇到问题自动尝试其他方法")
        print("• ✅ 结构化结果输出 - 清晰的信息整理")
        
        return 0
    else:
        print("\n❌ 任务执行失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
