import asyncio
from playwright.async_api import async_playwright

async def run():
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()

        # 访问地点页面
        await page.goto("https://www.lonelyplanet.com/places/")

        # 获取所有地点链接的XPath
        places_xpath = "//main//ul/li/article/div/a"
        places = await page.query_selector_all(places_xpath)

        results = []

        for i in range(len(places)):
            # 重新获取地点链接，防止元素失效
            places = await page.query_selector_all(places_xpath)
            place = places[i]

            # 获取地点链接地址
            href = await place.get_attribute("href")
            if not href.startswith("http"):
                href = "https://www.lonelyplanet.com" + href

            # 进入地点详情页
            await page.goto(href)

            # 查找best time to visit文章链接
            best_time_xpath = "//a[contains(@href, 'best-time-to-visit')]"
            best_time_link = await page.query_selector(best_time_xpath)
            if best_time_link:
                best_time_href = await best_time_link.get_attribute("href")
                if not best_time_href.startswith("http"):
                    best_time_href = "https://www.lonelyplanet.com" + best_time_href

                # 进入best time to visit文章页
                await page.goto(best_time_href)

                # 抓取文章标题和正文内容
                title_xpath = "//main//header//h1"
                content_xpath = "//main//div[contains(@class, 'article-body')]//p"

                title_element = await page.query_selector(title_xpath)
                title = await title_element.text_content() if title_element else "No Title"

                content_elements = await page.query_selector_all(content_xpath)
                content = "\n".join([await el.text_content() for el in content_elements])

                results.append({
                    "place_url": href,
                    "article_url": best_time_href,
                    "title": title.strip(),
                    "content": content.strip()
                })

                # 返回地点详情页，继续下一个地点
                await page.goto(href)

        await browser.close()

        # 打印结果
        for item in results:
            print(f"地点页面: {item['place_url']}")
            print(f"文章页面: {item['article_url']}")
            print(f"标题: {item['title']}")
            print(f"内容: {item['content'][:500]}...")  # 只打印前500字符
            print("="*80)

asyncio.run(run())