from playwright.sync_api import sync_playwright

def test_signup_flow():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        context = browser.new_context()  # 如果需要预存登录状态
        page = context.new_page()

        # 自动生成的步骤
        page.goto("https://demo.playwright.dev/todomvc")
        page.get_by_placeholder("What needs to be done?").fill("Write tests faster")
        page.get_by_placeholder("What needs to be done?").press("Enter")
        page.get_by_role("checkbox", name="Toggle Todo").check()

        # 自动生成的断言
        assert page.get_by_role("listitem").nth(0).inner_text() == "Write tests faster"

        context.close()
        browser.close()
