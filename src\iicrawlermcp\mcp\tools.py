"""
MCP工具实现

实现了通过MCP协议暴露的工具函数，主要包括：
- intelligent_web_task: 统一的智能网页任务接口
- browser_status: 浏览器状态查询
- take_screenshot: 快速截图
- cleanup_browser: 资源清理
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


async def intelligent_web_task(task_description: str) -> str:
    """
    执行智能网页任务 - 统一入口
    
    这个工具可以处理任何网页相关的任务，包括：
    - 网页导航和浏览
    - 元素查找和交互  
    - 数据提取和分析
    - 截图和页面操作
    
    Args:
        task_description: 自然语言描述的任务，例如：
            "打开google.com并搜索Python"
            "在淘宝上找到iPhone 15的价格"
            "截取当前页面的截图"
            "填写表单并提交"
    
    Returns:
        任务执行结果的详细描述
    """
    try:
        logger.info(f"🤖 执行智能网页任务: {task_description}")
        
        # 导入CrawlerAgent
        from ..agents import build_agent
        
        # 创建CrawlerAgent实例
        crawler_agent = build_agent()
        
        # 执行任务 - CrawlerAgent会自动委托给合适的子Agent
        result = crawler_agent.invoke(task_description)
        
        # 提取输出结果
        output = result.get('output', str(result)) if isinstance(result, dict) else str(result)
        
        logger.info(f"✅ 智能网页任务完成")
        return output
        
    except Exception as e:
        error_msg = f"❌ 智能网页任务执行失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


async def browser_status() -> str:
    """
    获取当前浏览器状态
    
    Returns:
        浏览器状态信息，包括当前URL、是否初始化等
    """
    try:
        from ..core.browser import get_global_browser
        
        browser = get_global_browser()
        
        if not browser._is_initialized:
            return "🔴 浏览器未初始化"
        
        try:
            current_url = browser.get_current_url()
            page_title = browser.get_page_title()
            
            status_info = f"""🟢 浏览器状态正常
📍 当前URL: {current_url}
📄 页面标题: {page_title}"""
            
            return status_info
            
        except Exception as e:
            return f"🟡 浏览器已初始化，但获取页面信息失败: {str(e)}"
            
    except Exception as e:
        error_msg = f"❌ 获取浏览器状态失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


async def take_screenshot(filename: str = "screenshot.png") -> str:
    """
    快速截图工具
    
    Args:
        filename: 截图文件名，默认为screenshot.png
        
    Returns:
        截图操作结果
    """
    try:
        logger.info(f"📸 执行快速截图: {filename}")
        
        from ..tools.browser_tools import take_screenshot_advanced
        
        # 执行截图
        result = take_screenshot_advanced(filename)
        
        success_msg = f"✅ 截图已保存: {filename}"
        logger.info(success_msg)
        return success_msg
        
    except Exception as e:
        error_msg = f"❌ 截图失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


async def cleanup_browser() -> str:
    """
    清理浏览器资源
    
    Returns:
        清理操作结果
    """
    try:
        logger.info("🧹 清理浏览器资源")
        
        from ..core.browser import close_global_browser
        
        # 关闭全局浏览器
        close_global_browser()
        
        success_msg = "✅ 浏览器资源已清理"
        logger.info(success_msg)
        return success_msg
        
    except Exception as e:
        error_msg = f"❌ 清理浏览器资源失败: {str(e)}"
        logger.error(error_msg)
        return error_msg


# 工具函数映射表
TOOL_FUNCTIONS = {
    "intelligent_web_task": intelligent_web_task,
    "browser_status": browser_status,
    "take_screenshot": take_screenshot,
    "cleanup_browser": cleanup_browser
}


async def execute_tool(tool_name: str, arguments: Dict[str, Any]) -> str:
    """
    执行指定的工具
    
    Args:
        tool_name: 工具名称
        arguments: 工具参数
        
    Returns:
        工具执行结果
        
    Raises:
        ValueError: 如果工具名称不存在
    """
    if tool_name not in TOOL_FUNCTIONS:
        raise ValueError(f"未知工具: {tool_name}")
    
    tool_func = TOOL_FUNCTIONS[tool_name]
    
    try:
        # 根据工具类型调用相应函数
        if tool_name == "intelligent_web_task":
            return await tool_func(arguments.get("task_description", ""))
        elif tool_name == "take_screenshot":
            return await tool_func(arguments.get("filename", "screenshot.png"))
        else:
            # browser_status 和 cleanup_browser 不需要参数
            return await tool_func()
            
    except Exception as e:
        logger.error(f"工具执行异常: {tool_name}, 错误: {e}")
        raise
