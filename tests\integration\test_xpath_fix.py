#!/usr/bin/env python3
"""
测试XPath修复的简单脚本
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_xpath_fix():
    """
    测试XPath修复是否工作
    """
    
    print("🔧 测试XPath修复")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 简单的任务测试
        task_description = """
        请执行以下简单任务来测试XPath修复：
        
        1. 导航到 https://www.google.com/
        2. 找到搜索框并输入"test"
        3. 截取一个屏幕截图
        
        这个测试主要是验证XPath选择器是否能正常工作。
        """
        
        print("\n🚀 开始执行测试任务...")
        print("📋 任务：测试XPath选择器修复")
        
        # 执行任务
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 测试完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"XPath test error: {e}")
        return None


if __name__ == "__main__":
    result = test_xpath_fix()
    if result:
        print("\n🎉 XPath修复测试成功！")
    else:
        print("\n❌ XPath修复测试失败")
