# 部署指南

## 📋 概述

本文档提供iICrawlerMCP系统在生产环境中的部署指南，包括单机部署、容器化部署和Kubernetes集群部署。

## 🏗️ 部署架构

### 单机部署架构
```
┌─────────────────────────────────────┐
│            单机服务器                │
├─────────────────────────────────────┤
│  Web服务 (FastAPI + Uvicorn)       │
│  Agent服务 (Python Workers)        │
│  数据库 (PostgreSQL)               │
│  缓存 (Redis)                      │
│  监控 (Prometheus + Grafana)       │
└─────────────────────────────────────┘
```

### 容器化部署架构
```
┌─────────────────────────────────────┐
│            Docker Host              │
├─────────────────────────────────────┤
│  ┌─────────┐ ┌─────────┐ ┌─────────┐│
│  │Web容器  │ │Agent容器│ │DB容器   ││
│  │FastAPI  │ │Workers  │ │Postgres ││
│  └─────────┘ └─────────┘ └─────────┘│
│  ┌─────────┐ ┌─────────┐ ┌─────────┐│
│  │Redis容器│ │监控容器 │ │Nginx容器││
│  │Cache    │ │Monitor  │ │Proxy    ││
│  └─────────┘ └─────────┘ └─────────┘│
└─────────────────────────────────────┘
```

### Kubernetes集群架构
```
┌─────────────────────────────────────┐
│         Kubernetes Cluster         │
├─────────────────────────────────────┤
│  ┌─────────────────────────────────┐│
│  │        Ingress Controller       ││
│  └─────────────────────────────────┘│
│  ┌─────────┐ ┌─────────┐ ┌─────────┐│
│  │Web Pods │ │Agent Pods│ │Monitor ││
│  │(3 replicas)│(5 replicas)│Pods   ││
│  └─────────┘ └─────────┘ └─────────┘│
│  ┌─────────────────────────────────┐│
│  │     Persistent Storage          ││
│  │  PostgreSQL + Redis Cluster    ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

## 🚀 单机部署

### 系统要求
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 50GB可用空间
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+

### 部署步骤

#### 1. 系统准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必要软件
sudo apt install -y python3.11 python3.11-venv python3-pip
sudo apt install -y postgresql postgresql-contrib redis-server
sudo apt install -y nginx supervisor

# 安装Node.js (用于前端)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

#### 2. 数据库配置
```bash
# 配置PostgreSQL
sudo -u postgres createuser --interactive iicrawler
sudo -u postgres createdb iicrawler_prod
sudo -u postgres psql -c "ALTER USER iicrawler PASSWORD 'your_secure_password';"

# 配置Redis
sudo systemctl enable redis-server
sudo systemctl start redis-server

# 修改Redis配置
sudo nano /etc/redis/redis.conf
# 设置: requirepass your_redis_password
sudo systemctl restart redis-server
```

#### 3. 应用部署
```bash
# 创建应用目录
sudo mkdir -p /opt/iicrawlermcp
sudo chown $USER:$USER /opt/iicrawlermcp
cd /opt/iicrawlermcp

# 克隆代码
git clone https://github.com/your-org/iicrawlermcp.git .

# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
playwright install chromium

# 配置环境变量
cp .env.example .env.prod
nano .env.prod
```

#### 4. 环境变量配置
```bash
# .env.prod
DATABASE_URL=postgresql://iicrawler:your_secure_password@localhost:5432/iicrawler_prod
REDIS_URL=redis://:your_redis_password@localhost:6379/0

# API配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 安全配置
SECRET_KEY=your_very_long_secret_key_here
ALLOWED_HOSTS=your-domain.com,localhost

# 生产环境配置
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# 浏览器配置
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
```

#### 5. 数据库初始化
```bash
# 运行数据库迁移
source venv/bin/activate
python -m alembic upgrade head

# 创建超级用户
python scripts/create_superuser.py
```

#### 6. 服务配置

**Supervisor配置** (`/etc/supervisor/conf.d/iicrawlermcp.conf`):
```ini
[program:iicrawlermcp-web]
command=/opt/iicrawlermcp/venv/bin/uvicorn src.iicrawlermcp.main:app --host 0.0.0.0 --port 8000
directory=/opt/iicrawlermcp
user=iicrawler
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/iicrawlermcp/web.log

[program:iicrawlermcp-worker]
command=/opt/iicrawlermcp/venv/bin/python -m src.iicrawlermcp.workers.agent_worker
directory=/opt/iicrawlermcp
user=iicrawler
autostart=true
autorestart=true
numprocs=4
process_name=%(program_name)s_%(process_num)02d
redirect_stderr=true
stdout_logfile=/var/log/iicrawlermcp/worker.log
```

**Nginx配置** (`/etc/nginx/sites-available/iicrawlermcp`):
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # 静态文件
    location /static/ {
        alias /opt/iicrawlermcp/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket代理
    location /ws {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
    
    # 前端应用
    location / {
        try_files $uri $uri/ /index.html;
        root /opt/iicrawlermcp/frontend/dist;
    }
}
```

#### 7. 启动服务
```bash
# 创建日志目录
sudo mkdir -p /var/log/iicrawlermcp
sudo chown iicrawler:iicrawler /var/log/iicrawlermcp

# 启动服务
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start all

# 启用Nginx
sudo ln -s /etc/nginx/sites-available/iicrawlermcp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🐳 Docker部署

### Docker Compose配置

**docker-compose.prod.yml**:
```yaml
version: '3.8'

services:
  web:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql://iicrawler:${DB_PASSWORD}@db:5432/iicrawler
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    
  worker:
    build: 
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - DATABASE_URL=postgresql://iicrawler:${DB_PASSWORD}@db:5432/iicrawler
      - REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    deploy:
      replicas: 3
      
  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=iicrawler
      - POSTGRES_USER=iicrawler
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    
  redis:
    image: redis:7-alpine
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - ./frontend/dist:/usr/share/nginx/html
    depends_on:
      - web
    restart: unless-stopped
    
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped
    
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

### 部署命令
```bash
# 创建环境变量文件
cat > .env.prod << EOF
DB_PASSWORD=your_secure_db_password
REDIS_PASSWORD=your_secure_redis_password
GRAFANA_PASSWORD=your_grafana_password
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
EOF

# 构建和启动服务
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f web
```

## ☸️ Kubernetes部署

### 命名空间和配置
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: iicrawlermcp

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: iicrawlermcp-config
  namespace: iicrawlermcp
data:
  DATABASE_URL: "*********************************************/iicrawler"
  REDIS_URL: "redis://:password@redis:6379/0"
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
```

### 数据库部署
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: iicrawlermcp
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15
        env:
        - name: POSTGRES_DB
          value: iicrawler
        - name: POSTGRES_USER
          value: iicrawler
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: iicrawlermcp-secrets
              key: db-password
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi

---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: iicrawlermcp
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

### 应用部署
```yaml
# web-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iicrawlermcp-web
  namespace: iicrawlermcp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: iicrawlermcp-web
  template:
    metadata:
      labels:
        app: iicrawlermcp-web
    spec:
      containers:
      - name: web
        image: iicrawlermcp:latest
        ports:
        - containerPort: 8000
        envFrom:
        - configMapRef:
            name: iicrawlermcp-config
        - secretRef:
            name: iicrawlermcp-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: iicrawlermcp-web-service
  namespace: iicrawlermcp
spec:
  selector:
    app: iicrawlermcp-web
  ports:
  - port: 80
    targetPort: 8000
  type: ClusterIP
```

### Ingress配置
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: iicrawlermcp-ingress
  namespace: iicrawlermcp
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: iicrawlermcp-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: iicrawlermcp-web-service
            port:
              number: 80
```

### 部署命令
```bash
# 应用配置
kubectl apply -f namespace.yaml
kubectl apply -f configmap.yaml
kubectl apply -f secrets.yaml
kubectl apply -f postgres.yaml
kubectl apply -f redis.yaml
kubectl apply -f web-deployment.yaml
kubectl apply -f worker-deployment.yaml
kubectl apply -f ingress.yaml

# 检查部署状态
kubectl get pods -n iicrawlermcp
kubectl get services -n iicrawlermcp
kubectl get ingress -n iicrawlermcp

# 查看日志
kubectl logs -f deployment/iicrawlermcp-web -n iicrawlermcp
```

## 🔧 部署后配置

### SSL证书配置
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 监控配置
```bash
# 配置Prometheus监控
curl -X POST http://localhost:9090/api/v1/admin/tsdb/snapshot

# 配置Grafana仪表板
# 导入仪表板ID: 12345 (iICrawlerMCP Dashboard)
```

### 备份配置
```bash
# 数据库备份脚本
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份
pg_dump -h localhost -U iicrawler iicrawler_prod > $BACKUP_DIR/db_backup_$DATE.sql

# 保留最近7天的备份
find $BACKUP_DIR -name "db_backup_*.sql" -mtime +7 -delete
```

## 📊 部署验证

### 健康检查
```bash
# API健康检查
curl http://your-domain.com/health

# 数据库连接检查
curl http://your-domain.com/api/health/database

# Redis连接检查
curl http://your-domain.com/api/health/redis

# 完整系统检查
curl http://your-domain.com/api/health/full
```

### 性能测试
```bash
# 使用Apache Bench进行负载测试
ab -n 1000 -c 10 http://your-domain.com/api/health

# 使用wrk进行压力测试
wrk -t12 -c400 -d30s http://your-domain.com/api/health
```

---

*最后更新: 2025-01-29*
