#!/usr/bin/env python3
"""
测试元素点击功能
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.core.browser import Browser
from iicrawlermcp.dom.extractor import DOMExtractor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_element_clicking():
    """
    直接测试元素点击功能
    """
    
    print("🖱️ 元素点击测试")
    print("=" * 50)
    
    try:
        # 创建Browser实例
        print("\n🌐 创建Browser实例...")
        browser = Browser(headless=False)
        
        # 导航到测试页面
        print("📍 导航到httpbin表单页面...")
        browser.navigate("https://httpbin.org/forms/post")
        
        # 创建DOM提取器
        print("🔍 创建DOM提取器...")
        dom_extractor = DOMExtractor(browser)
        
        # 获取页面元素信息
        print("📊 分析页面元素...")
        interactive_elements = dom_extractor.get_interactive_elements()
        
        print(f"找到 {len(interactive_elements)} 个交互元素")
        
        # 查找单选按钮
        radio_buttons = [elem for elem in interactive_elements 
                        if elem.tag_name.lower() == 'input' and 
                        elem.attributes.get('type') == 'radio']
        
        print(f"找到 {len(radio_buttons)} 个单选按钮:")
        for i, radio in enumerate(radio_buttons):
            print(f"  {i+1}. {radio.text_content} - value: {radio.attributes.get('value')} - xpath: {radio.xpath}")
        
        # 尝试点击Large选项
        large_radio = None
        for radio in radio_buttons:
            if radio.attributes.get('value') == 'large':
                large_radio = radio
                break
        
        if large_radio:
            print(f"\n🎯 找到Large单选按钮: {large_radio.xpath}")
            
            # 尝试不同的选择器
            selectors_to_try = [
                large_radio.xpath,  # XPath
                f"input[value='large']",  # CSS属性选择器
                f"input[name='size'][value='large']",  # 更具体的CSS选择器
            ]
            
            for i, selector in enumerate(selectors_to_try, 1):
                print(f"\n🧪 尝试选择器 {i}: {selector}")
                try:
                    # 等待元素出现
                    browser.wait_for_element(selector, timeout=5000)
                    print(f"✅ 元素存在")
                    
                    # 尝试点击
                    browser.click(selector)
                    print(f"✅ 点击成功")
                    
                    # 截图验证
                    screenshot_path = browser.screenshot(f"click_test_{i}.png")
                    print(f"📸 截图保存: {screenshot_path}")
                    
                    break
                    
                except Exception as e:
                    print(f"❌ 选择器失败: {e}")
        else:
            print("❌ 未找到Large单选按钮")
        
        # 测试复选框
        print("\n☑️ 测试复选框...")
        checkboxes = [elem for elem in interactive_elements 
                     if elem.tag_name.lower() == 'input' and 
                     elem.attributes.get('type') == 'checkbox']
        
        print(f"找到 {len(checkboxes)} 个复选框:")
        for i, checkbox in enumerate(checkboxes):
            print(f"  {i+1}. {checkbox.text_content} - value: {checkbox.attributes.get('value')} - xpath: {checkbox.xpath}")
        
        # 尝试点击Bacon复选框
        bacon_checkbox = None
        for checkbox in checkboxes:
            if checkbox.attributes.get('value') == 'bacon':
                bacon_checkbox = checkbox
                break
        
        if bacon_checkbox:
            print(f"\n🥓 找到Bacon复选框: {bacon_checkbox.xpath}")
            try:
                browser.click(bacon_checkbox.xpath)
                print("✅ Bacon复选框点击成功")
                
                screenshot_path = browser.screenshot("bacon_checkbox_test.png")
                print(f"📸 截图保存: {screenshot_path}")
                
            except Exception as e:
                print(f"❌ Bacon复选框点击失败: {e}")
        
        # 测试提交按钮
        print("\n📤 测试提交按钮...")
        buttons = [elem for elem in interactive_elements 
                  if elem.tag_name.lower() == 'button']
        
        if buttons:
            submit_button = buttons[0]
            print(f"找到提交按钮: {submit_button.xpath}")
            print(f"按钮文本: {submit_button.text_content}")
            
            # 不实际点击提交按钮，只验证能找到
            print("✅ 提交按钮定位成功（未点击）")
        
        # 清理
        browser.close()
        print("\n✅ 测试完成")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Element clicking test error: {e}")
        return False


if __name__ == "__main__":
    result = test_element_clicking()
    if result:
        print("\n🎉 元素点击测试成功！")
    else:
        print("\n❌ 元素点击测试失败")
