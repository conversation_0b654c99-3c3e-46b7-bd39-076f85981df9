#!/usr/bin/env python3
"""
Basic ElementAgent Usage Example

This example demonstrates the basic usage of the new ElementAgent
for DOM element analysis and manipulation.
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_element_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Demonstrate basic ElementAgent functionality."""
    
    print("🤖 ElementAgent Basic Usage Example")
    print("=" * 50)
    
    try:
        # Validate configuration
        config.validate()
        print("✅ Configuration validated")
        
        # Build the element agent
        logger.info("Building ElementAgent...")
        element_agent = build_element_agent()
        print("✅ ElementAgent created successfully")
        
        # Test 1: Navigate to a test page first
        print("\n📍 Test 1: Navigation and Page Analysis")
        # First navigate using browser tools
        from iicrawlermcp.core.browser import get_global_browser
        browser = get_global_browser()
        browser.navigate("https://httpbin.org/forms/post")
        print("✅ Navigated to test page")

        # Then analyze the page structure
        result = element_agent.invoke("Analyze the current page structure")
        print(f"Result: {result['output']}")
        
        # Test 2: Find form elements
        print("\n📝 Test 2: Form Element Discovery")
        result = element_agent.invoke("Find all form elements on the page and describe them")
        print(f"Result: {result['output']}")
        
        # Test 3: Find interactive elements
        print("\n🖱️ Test 3: Interactive Element Discovery")
        result = element_agent.invoke("Find all interactive elements that a user can click or interact with")
        print(f"Result: {result['output']}")
        
        # Test 4: Smart element search
        print("\n🎯 Test 4: Smart Element Search")
        result = element_agent.invoke("Find the submit button on this form")
        print(f"Result: {result['output']}")
        
        # Test 5: Page summary
        print("\n📊 Test 5: Page Summary")
        result = element_agent.invoke("Give me a comprehensive summary of this page's DOM structure")
        print(f"Result: {result['output']}")
        
        # Clean up
        element_agent.cleanup()
        print("\n✅ ElementAgent demo completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        logger.error(f"ElementAgent demo error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
