#!/usr/bin/env python3
"""
Basic Browser Usage Example

This example demonstrates the basic usage of iICrawlerMCP browser functionality.
Perfect for getting started with the library.
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.core.browser import get_global_browser
from iicrawlermcp.core.config import config

def basic_browser_example():
    """Demonstrate basic browser operations."""
    print("🌐 Basic Browser Usage Example")
    print("=" * 40)
    
    try:
        # Validate configuration
        config.validate()
        print("✅ Configuration validated")
        
        # Get browser instance
        print("📱 Creating browser instance...")
        browser = get_global_browser()
        
        # Navigate to a test page
        print("🔗 Navigating to test page...")
        browser.navigate("https://httpbin.org/html")
        
        # Get page information
        print("📄 Getting page information...")
        page_info = browser.get_page_info()
        print(f"  Title: {page_info.get('title', 'N/A')}")
        print(f"  URL: {page_info.get('url', 'N/A')}")
        
        # Take a screenshot
        print("📸 Taking screenshot...")
        screenshot_path = browser.screenshot()
        print(f"  Screenshot saved: {screenshot_path}")
        
        # Clean up
        browser.close()
        print("✅ Browser closed successfully")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    print("🎯 iICrawlerMCP Basic Browser Example")
    print("This example shows basic browser automation capabilities.\n")
    
    basic_browser_example()
    
    print("\n🎉 Example completed!")
    print("💡 Next: Try examples/basic/agent_basics.py for agent usage")

if __name__ == "__main__":
    main()
