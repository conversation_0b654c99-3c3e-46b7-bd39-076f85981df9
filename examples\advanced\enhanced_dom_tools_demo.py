#!/usr/bin/env python3
"""
Demo script showing the enhanced DOM tools in action.

This script demonstrates the layered architecture and improved element detection.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

async def demo_enhanced_tools():
    """Demonstrate enhanced DOM tools with a real webpage."""
    
    print("🚀 Enhanced DOM Tools Demo")
    print("="*50)
    
    try:
        # Import necessary modules
        from iicrawlermcp.core.browser import get_global_browser
        from iicrawlermcp.agents.element_agent import build_element_agent
        
        # Initialize browser
        print("🌐 Initializing browser...")
        browser = get_global_browser()
        browser._initialize()
        
        # Navigate to a test page
        test_url = "https://httpbin.org/forms/post"
        print(f"📍 Navigating to: {test_url}")
        await browser.navigate(test_url)
        
        # Build ElementAgent with enhanced tools
        print("🤖 Building ElementAgent with enhanced tools...")
        agent = build_element_agent()
        
        # Demo 1: Compare old vs new actionable elements detection
        print("\n" + "="*50)
        print("📊 DEMO 1: Enhanced Actionable Elements Detection")
        print("="*50)
        
        result = agent.invoke("""
        请使用增强的DOM工具来分析这个页面的交互元素。
        
        请按以下步骤操作：
        1. 使用 dom_get_interactive_elements_smart 获取最智能的交互元素检测结果
        2. 使用 dom_get_form_elements 专门分析表单元素
        3. 使用 dom_get_clickable_elements 获取所有可点击元素
        4. 比较这些结果，总结页面的交互能力
        
        请提供简洁的分析报告。
        """)
        
        print("🔍 智能交互元素分析结果:")
        print(result.get('output', str(result)))
        
        # Demo 2: Layered element detection
        print("\n" + "="*50)
        print("📊 DEMO 2: 分层元素检测")
        print("="*50)
        
        result = agent.invoke("""
        请展示分层DOM工具的威力：
        
        基础层：
        1. 使用 dom_get_visible_elements 获取所有可见元素概览
        
        分类层：
        2. 使用 dom_get_buttons_enhanced 获取增强的按钮检测
        3. 使用 dom_get_inputs_enhanced 获取增强的输入框检测
        
        功能层：
        4. 使用 dom_get_focusable_elements 获取可获得焦点的元素
        
        请为每一层提供简洁的总结。
        """)
        
        print("🏗️ 分层元素检测结果:")
        print(result.get('output', str(result)))
        
        # Demo 3: Smart element finding
        print("\n" + "="*50)
        print("📊 DEMO 3: 智能元素查找")
        print("="*50)
        
        result = agent.invoke("""
        请使用智能元素查找功能：
        
        1. 找到页面上的提交按钮
        2. 找到所有的输入框
        3. 找到表单相关的元素
        
        请使用最合适的增强工具，并提供每个元素的xpath选择器。
        """)
        
        print("🎯 智能元素查找结果:")
        print(result.get('output', str(result)))
        
        print("\n" + "="*50)
        print("✅ Demo completed successfully!")
        print("="*50)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            browser.close()
            print("🧹 Browser cleanup completed")
        except:
            pass


def demo_tool_comparison():
    """Compare old vs new tools without browser."""
    
    print("\n🔄 Tool Comparison Demo")
    print("="*50)
    
    try:
        # Import enhanced tools only (dom_tools removed)
        from iicrawlermcp.tools.dom_tools_enhanced import ENHANCED_DOM_TOOLS

        print("📊 Tool Inventory:")
        print(f"   Original DOM tools: REMOVED")
        print(f"   Enhanced DOM tools: {len(ENHANCED_DOM_TOOLS)}")
        print(f"   Total available: {len(ENHANCED_DOM_TOOLS)}")

        print("\n⚡ Enhanced Tools:")
        for tool in ENHANCED_DOM_TOOLS:
            print(f"   - {tool.name}")
        
        print("\n🎯 Key Improvements:")
        print("   ✅ Layered architecture (Foundation → Classification → Functional)")
        print("   ✅ Smart context-aware detection")
        print("   ✅ Enhanced filtering options")
        print("   ✅ Priority-based sorting")
        print("   ✅ Comprehensive element coverage")
        print("   ✅ Backward compatibility maintained")
        
    except Exception as e:
        print(f"❌ Tool comparison failed: {e}")


def main():
    """Run the demo."""
    
    print("🎭 Enhanced DOM Tools Demonstration")
    print("="*60)
    
    # Demo 1: Tool comparison (no browser needed)
    demo_tool_comparison()
    
    # Demo 2: Interactive demo (requires browser)
    print("\n🤔 Would you like to run the interactive browser demo?")
    print("   This will open a browser and test the tools on a real webpage.")
    
    try:
        import asyncio
        
        # For demo purposes, let's run a simplified version
        print("\n📝 Running simplified demo...")
        
        # Show agent integration
        from iicrawlermcp.agents.element_agent import build_element_agent
        from iicrawlermcp.agents.crawler_agent import build_agent
        
        print("🤖 Testing agent integration...")
        element_agent = build_element_agent()
        crawler_agent = build_agent()
        
        print(f"   ✅ ElementAgent: {len(element_agent.tools)} tools available")
        print(f"   ✅ CrawlerAgent: {len(crawler_agent.tools)} tools available")
        
        # Check for enhanced tools
        element_tool_names = [tool.name for tool in element_agent.tools]
        enhanced_tools_found = [name for name in element_tool_names if 'enhanced' in name or 'smart' in name]
        
        print(f"   ✅ Enhanced tools in ElementAgent: {len(enhanced_tools_found)}")
        for tool in enhanced_tools_found:
            print(f"      - {tool}")
        
        print("\n🎉 Demo completed! Enhanced tools are ready to use.")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
