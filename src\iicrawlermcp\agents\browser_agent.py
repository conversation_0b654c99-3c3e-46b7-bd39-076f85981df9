"""
Browser Agent module for iICrawlerMCP.

This module provides a specialized agent for browser control and automation tasks.
The BrowserAgent focuses specifically on browser operations like navigation,
interaction, and state management.
"""

import logging
from typing import Optional, List, Dict, Any
from langchain import hub
from langchain.agents import create_openai_functions_agent, AgentExecutor
from langchain_openai import Chat<PERSON>penAI
from langchain_core.tools import BaseTool

from ..core.config import config
from ..core.browser import get_global_browser

logger = logging.getLogger(__name__)


class BrowserAgent:
    """
    A specialized agent for browser control and automation.
    
    This agent focuses on browser-specific operations including:
    - Page navigation and management
    - Element interaction (click, type, hover)
    - JavaScript execution
    - Screenshot and snapshot capture
    - Browser state management
    """
    
    def __init__(
        self, 
        tools: Optional[List[BaseTool]] = None,
        verbose: Optional[bool] = None,
        llm_config: Optional[dict] = None
    ):
        """
        Initialize the BrowserAgent.
        
        Args:
            tools: List of LangChain tools to use. If None, uses browser-specific tools.
            verbose: Whether to enable verbose logging. If None, uses config default.
            llm_config: Custom LLM configuration. If None, uses config defaults.
        """
        self.tools = tools or self._get_browser_tools()
        self.verbose = verbose if verbose is not None else config.VERBOSE
        self.llm_config = llm_config or config.get_llm_config()
        
        self._llm = None
        self._agent = None
        self._executor = None
        self._browser = None
    
    def _get_browser_tools(self) -> List[BaseTool]:
        """Get browser-specific tools."""
        from ..tools.browser_tools import BrowserToolkit

        # Get the basic browser tools for specialized agents
        return BrowserToolkit.get_basic_tools()
    
    def _create_llm(self) -> ChatOpenAI:
        """Create and configure the LLM instance."""
        if self._llm is None:
            try:
                self._llm = ChatOpenAI(**self.llm_config)
                logger.info(f"BrowserAgent LLM created with model: {self.llm_config.get('model')}")
            except Exception as e:
                logger.error(f"Failed to create BrowserAgent LLM: {e}")
                raise
        return self._llm
    
    def _create_agent(self) -> None:
        """Create the LangChain agent with browser-specific prompt."""
        if self._agent is None:
            try:
                llm = self._create_llm()
                # Use a browser-specific prompt or the default one
                prompt = hub.pull("hwchase17/openai-functions-agent")
                self._agent = create_openai_functions_agent(llm, self.tools, prompt)
                logger.info("BrowserAgent created successfully")
            except Exception as e:
                logger.error(f"Failed to create BrowserAgent: {e}")
                raise
    
    def _create_executor(self) -> AgentExecutor:
        """Create the agent executor."""
        if self._executor is None:
            self._create_agent()
            try:
                self._executor = AgentExecutor(
                    agent=self._agent,
                    tools=self.tools,
                    verbose=self.verbose,
                    max_iterations=50,  # 增加最大迭代次数，支持复杂任务
                    handle_parsing_errors=True
                )
                logger.info("BrowserAgent executor created successfully")
            except Exception as e:
                logger.error(f"Failed to create BrowserAgent executor: {e}")
                raise
        return self._executor
    
    def get_browser(self):
        """Get the browser instance managed by this agent."""
        if self._browser is None:
            self._browser = get_global_browser()
        return self._browser
    
    def navigate_to(self, url: str) -> Dict[str, Any]:
        """
        Navigate to a specific URL.
        
        Args:
            url: The URL to navigate to
            
        Returns:
            Navigation result information
        """
        try:
            browser = self.get_browser()
            result = browser.navigate(url)
            logger.info(f"BrowserAgent navigated to: {url}")
            return result
        except Exception as e:
            logger.error(f"BrowserAgent navigation failed: {e}")
            raise
    
    def take_screenshot(self, filename: Optional[str] = None, full_page: bool = True) -> str:
        """
        Take a screenshot of the current page.
        
        Args:
            filename: Optional filename for the screenshot
            full_page: Whether to capture the full page
            
        Returns:
            Path to the saved screenshot
        """
        try:
            browser = self.get_browser()
            result = browser.screenshot(path=filename, full_page=full_page)
            logger.info(f"BrowserAgent took screenshot: {result}")
            return result
        except Exception as e:
            logger.error(f"BrowserAgent screenshot failed: {e}")
            raise
    
    def get_page_info(self) -> Dict[str, str]:
        """
        Get current page information.

        Returns:
            Dictionary with page title and URL
        """
        try:
            browser = self.get_browser()
            page_info = browser.get_page_info()
            return {
                'title': page_info.get('title', 'N/A'),
                'url': page_info.get('url', 'N/A')
            }
        except Exception as e:
            logger.error(f"BrowserAgent get_page_info failed: {e}")
            raise

    def click_element(self, element_selector: str) -> bool:
        """
        Click on an element.

        Args:
            element_selector: complete xpath selector for the element to click

        Returns:
            True if click was successful
        """
        try:
            browser = self.get_browser()
            browser.click(element_selector)
            logger.info(f"BrowserAgent clicked element: {element_selector}")
            return True
        except Exception as e:
            logger.error(f"BrowserAgent click failed: {e}")
            raise

    def type_text(self, element_selector: str, text: str, clear_first: bool = True) -> bool:
        """
        Type text into an element.

        Args:
            element_selector: xpath selector for the element
            text: Text to type
            clear_first: Whether to clear existing text first

        Returns:
            True if typing was successful
        """
        try:
            browser = self.get_browser()
            browser.type_text(element_selector, text, clear_first)
            logger.info(f"BrowserAgent typed text into: {element_selector}")
            return True
        except Exception as e:
            logger.error(f"BrowserAgent type_text failed: {e}")
            raise

    def wait_for_element(self, element_selector: str, timeout: int = 30000) -> bool:
        """
        Wait for an element to appear.

        Args:
            element_selector: Xpath selector for the element
            timeout: Timeout in milliseconds

        Returns:
            True if element appeared within timeout
        """
        try:
            browser = self.get_browser()
            browser.wait_for_element(element_selector, timeout)
            logger.info(f"BrowserAgent found element: {element_selector}")
            return True
        except Exception as e:
            logger.error(f"BrowserAgent wait_for_element failed: {e}")
            raise

    def execute_script(self, script: str) -> Any:
        """
        Execute JavaScript on the page.

        Args:
            script: JavaScript code to execute

        Returns:
            Result of the script execution
        """
        try:
            browser = self.get_browser()
            result = browser.evaluate_script(script)
            logger.info(f"BrowserAgent executed script successfully")
            return result
        except Exception as e:
            logger.error(f"BrowserAgent execute_script failed: {e}")
            raise

    def invoke(self, input_text: str) -> dict:
        """
        Execute a browser automation task using the agent.

        Args:
            input_text: The task description or instruction for the browser agent.

        Returns:
            A dictionary containing the agent's response and output.
        """
        executor = self._create_executor()

        try:
            logger.info(f"BrowserAgent executing task: {input_text}")
            result = executor.invoke({"input": input_text})
            logger.info("BrowserAgent task completed successfully")
            return result
        except Exception as e:
            logger.error(f"BrowserAgent task execution failed: {e}")
            raise

    def cleanup(self) -> None:
        """Clean up resources used by the browser agent."""
        try:
            if self._browser:
                self._browser.close()
                self._browser = None
            logger.info("BrowserAgent cleanup completed")
        except Exception as e:
            logger.error(f"Error during BrowserAgent cleanup: {e}")


def build_browser_agent(
    tools: Optional[List[BaseTool]] = None,
    verbose: Optional[bool] = None,
    llm_config: Optional[dict] = None
) -> BrowserAgent:
    """
    Build and return a configured BrowserAgent instance.

    Args:
        tools: List of LangChain tools to use. If None, uses browser-specific tools.
        verbose: Whether to enable verbose logging. If None, uses config default.
        llm_config: Custom LLM configuration. If None, uses config defaults.

    Returns:
        A configured BrowserAgent instance.

    Example:
        browser_agent = build_browser_agent()
        result = browser_agent.invoke("Navigate to https://google.com and take a screenshot")
    """
    # Validate configuration before creating agent
    config.validate()

    try:
        agent = BrowserAgent(tools=tools, verbose=verbose, llm_config=llm_config)
        logger.info("BrowserAgent built successfully")
        return agent
    except Exception as e:
        logger.error(f"Failed to build BrowserAgent: {e}")
        raise
