#!/usr/bin/env python3
"""
最终高亮框清理功能测试
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_final_highlight_cleanup():
    """
    最终高亮框清理功能测试
    """
    
    print("🧹 最终高亮框清理功能测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 完整的测试任务
        task_description = """
        请执行以下完整的高亮框清理测试：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 分析页面上的表单元素（这会产生高亮框）
        3. 使用dom_clear_highlights工具清理高亮框
        4. 再次分析页面元素（验证自动清理）
        5. 截取最终页面截图
        
        请确保在步骤3中直接使用dom_clear_highlights工具，
        并在每次DOM分析时观察高亮框的变化。
        """
        
        print("\n🚀 开始最终高亮框清理测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Final highlight cleanup test error: {e}")
        return False


def main():
    """主函数"""
    
    print("🧹 最终高亮框清理功能测试")
    print("=" * 60)
    print("验证修复后的dom_clear_highlights工具")
    print("=" * 60)
    
    result = test_final_highlight_cleanup()
    
    if result:
        print("\n🎉 最终高亮框清理测试成功！")
        print("\n💡 修复和验证的功能：")
        print("• ✅ 修复了DOMExtractor中的_browser属性错误")
        print("• ✅ dom_clear_highlights工具正常工作")
        print("• ✅ JavaScript自动清理机制有效")
        print("• ✅ 高亮框累积问题得到解决")
        print("• ✅ Agent能够正确使用清理工具")
        
        print("\n🔧 技术修复总结：")
        print("• 🔧 修复了JavaScript中的cleanupHighlights函数")
        print("• 🔧 在DOM提取前自动调用清理函数")
        print("• 🔧 添加了dom_clear_highlights工具")
        print("• 🔧 修复了DOMExtractor的浏览器属性引用")
        
        return 0
    else:
        print("\n❌ 最终高亮框清理测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
