#!/usr/bin/env python3
"""
直接测试高亮框清理功能
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_direct_highlight_cleanup():
    """
    直接测试高亮框清理功能
    """
    
    print("🧹 直接高亮框清理测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 步骤1：导航和分析（产生高亮框）
        print("\n1️⃣ 导航并分析页面...")
        task1 = """
        导航到 https://httpbin.org/forms/post 并分析页面上的表单元素。
        """
        result1 = crawler_agent.invoke(task1)
        print("✅ 页面分析完成（应该有高亮框）")
        
        # 步骤2：直接使用dom_clear_highlights工具
        print("\n2️⃣ 直接使用dom_clear_highlights工具...")
        task2 = """
        直接使用dom_clear_highlights工具清理页面上的高亮框。
        不要委托给其他agent，直接调用dom_clear_highlights工具。
        """
        result2 = crawler_agent.invoke(task2)
        print("✅ 高亮框清理完成")
        
        # 步骤3：验证清理效果
        print("\n3️⃣ 验证清理效果...")
        task3 = """
        截取页面截图验证高亮框是否已被清理。
        """
        result3 = crawler_agent.invoke(task3)
        print("✅ 清理效果验证完成")
        
        print("\n📊 测试结果：")
        print("步骤1结果：", result1['output'][:100] + "...")
        print("步骤2结果：", result2['output'][:100] + "...")
        print("步骤3结果：", result3['output'][:100] + "...")
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Direct highlight cleanup test error: {e}")
        return False


def test_automatic_cleanup_verification():
    """
    测试自动清理机制验证
    """
    
    print("🔄 自动清理机制验证")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 多次DOM分析，验证自动清理
        print("\n🔄 执行多次DOM分析...")
        
        for i in range(2):
            print(f"\n第{i+1}次DOM分析...")
            task = f"""
            分析页面 https://httpbin.org/forms/post 上的表单元素。
            这是第{i+1}次分析，应该自动清理之前的高亮框。
            """
            result = crawler_agent.invoke(task)
            print(f"✅ 第{i+1}次分析完成")
        
        print("\n📸 最终验证截图...")
        final_task = """
        截取最终的页面截图，验证高亮框状态。
        """
        final_result = crawler_agent.invoke(final_task)
        print("✅ 最终验证完成")
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 自动清理验证失败: {e}")
        logger.error(f"Automatic cleanup verification error: {e}")
        return False


def main():
    """主函数"""
    
    print("🧹 直接高亮框清理测试")
    print("=" * 60)
    print("验证dom_clear_highlights工具和自动清理机制")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 直接清理工具测试")
    print("2. 自动清理机制验证")
    
    choice = input("\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n🧹 执行直接清理工具测试...")
        result = test_direct_highlight_cleanup()
    elif choice == "2":
        print("\n🔄 执行自动清理机制验证...")
        result = test_automatic_cleanup_verification()
    else:
        print("👋 无效选择，执行直接清理工具测试...")
        result = test_direct_highlight_cleanup()
    
    if result:
        print("\n🎉 直接高亮框清理测试成功！")
        print("\n💡 验证的功能：")
        print("• ✅ dom_clear_highlights工具正常工作")
        print("• ✅ CrawlerAgent能够直接使用清理工具")
        print("• ✅ 高亮框清理机制有效")
        print("• ✅ 自动清理功能正常")
        
        return 0
    else:
        print("\n❌ 直接高亮框清理测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
