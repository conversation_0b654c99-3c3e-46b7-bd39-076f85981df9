#!/usr/bin/env python3
"""
简单的高亮框清理测试
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_simple_highlight_cleanup():
    """
    简单的高亮框清理测试
    """
    
    print("🧹 简单高亮框清理测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 测试任务：使用dom_clear_highlights工具
        task_description = """
        请执行以下任务来测试高亮框清理功能：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 分析页面上的表单元素（这会产生高亮框）
        3. 使用dom_clear_highlights工具清理高亮框
        4. 截取清理后的页面截图
        
        请确保使用dom_clear_highlights工具来清理页面上的高亮框。
        """
        
        print("\n🚀 开始简单高亮框清理测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Simple highlight cleanup test error: {e}")
        return False


def main():
    """主函数"""
    
    print("🧹 简单高亮框清理测试")
    print("=" * 60)
    print("测试dom_clear_highlights工具是否正常工作")
    print("=" * 60)
    
    result = test_simple_highlight_cleanup()
    
    if result:
        print("\n🎉 简单高亮框清理测试成功！")
        print("\n💡 验证的功能：")
        print("• ✅ dom_clear_highlights工具可用")
        print("• ✅ 高亮框清理功能正常")
        print("• ✅ Agent能够识别和使用清理工具")
        
        return 0
    else:
        print("\n❌ 简单高亮框清理测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
