# 故障排除指南

## 🔧 常见问题解决方案

### 安装问题

#### Q1: pip install 失败，提示权限错误
**错误信息**: `Permission denied` 或 `Access is denied`

**解决方案**:
```bash
# 方案1: 使用用户安装
pip install --user -r requirements.txt

# 方案2: 使用虚拟环境
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate     # Windows
pip install -r requirements.txt
```

#### Q2: Playwright 浏览器下载失败
**错误信息**: `Failed to download browser` 或网络超时

**解决方案**:
```bash
# 方案1: 使用代理
PLAYWRIGHT_DOWNLOAD_HOST=https://playwright.azureedge.net playwright install

# 方案2: 手动设置镜像
export PLAYWRIGHT_DOWNLOAD_HOST=https://npmmirror.com/mirrors/playwright
playwright install

# 方案3: 仅安装必要浏览器
playwright install chromium
```

#### Q3: 依赖包版本冲突
**错误信息**: `Dependency conflicts` 或 `Version incompatible`

**解决方案**:
```bash
# 方案1: 清理并重新安装
pip uninstall -r requirements.txt -y
pip install -r requirements.txt

# 方案2: 使用 uv 管理依赖
pip install uv
uv sync

# 方案3: 创建新的虚拟环境
rm -rf .venv
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

### 运行时问题

#### Q4: 浏览器启动失败
**错误信息**: `Browser launch failed` 或 `Executable not found`

**解决方案**:
```bash
# 检查浏览器安装
playwright install --dry-run

# 安装系统依赖 (Linux)
playwright install-deps

# 使用有头模式调试
export BROWSER_HEADLESS=false

# 检查权限 (Linux)
sudo chmod +x ~/.cache/ms-playwright/*/chrome-linux/chrome
```

#### Q5: 数据库连接失败
**错误信息**: `Connection refused` 或 `Database not found`

**解决方案**:
```bash
# 检查数据库服务状态
sudo systemctl status postgresql  # Linux
brew services list | grep postgres  # macOS

# 启动数据库服务
sudo systemctl start postgresql  # Linux
brew services start postgresql   # macOS

# 检查连接配置
psql -h localhost -U your_user -d your_database

# 重置数据库
python -m alembic upgrade head
```

#### Q6: Redis 连接失败
**错误信息**: `Redis connection failed` 或 `Connection timeout`

**解决方案**:
```bash
# 检查 Redis 服务
redis-cli ping

# 启动 Redis 服务
sudo systemctl start redis  # Linux
brew services start redis   # macOS
redis-server                # 手动启动

# 检查配置
redis-cli -h localhost -p 6379 info
```

### 性能问题

#### Q7: 爬取速度慢
**症状**: 任务执行时间过长，响应缓慢

**解决方案**:
```python
# 1. 增加并发数
performance_config = {
    "concurrent_agents": 10,  # 增加到10个并发
    "batch_size": 50,         # 增加批处理大小
    "request_delay": 0.5      # 减少请求间隔
}

# 2. 启用缓存
cache_config = {
    "cache_enabled": True,
    "cache_ttl": 3600,        # 1小时缓存
    "cache_size": 1000        # 缓存1000个页面
}

# 3. 优化浏览器设置
browser_config = {
    "headless": True,         # 使用无头模式
    "disable_images": True,   # 禁用图片加载
    "disable_javascript": False,  # 根据需要禁用JS
    "timeout": 15000          # 减少超时时间
}
```

#### Q8: 内存使用过高
**症状**: 系统内存占用过高，可能出现OOM错误

**解决方案**:
```python
# 1. 限制并发数
performance_config = {
    "concurrent_agents": 3,   # 减少并发数
    "max_memory_mb": 2048,    # 限制最大内存使用
    "gc_threshold": 100       # 垃圾回收阈值
}

# 2. 启用内存监控
import psutil
import gc

def monitor_memory():
    memory = psutil.virtual_memory()
    if memory.percent > 80:
        gc.collect()  # 强制垃圾回收
        
# 3. 分批处理大任务
def process_large_task(items, batch_size=20):
    for i in range(0, len(items), batch_size):
        batch = items[i:i+batch_size]
        process_batch(batch)
        gc.collect()  # 每批处理后清理内存
```

### 网络问题

#### Q9: 网络连接超时
**错误信息**: `Connection timeout` 或 `Network unreachable`

**解决方案**:
```python
# 1. 增加超时时间
network_config = {
    "timeout": 60000,         # 60秒超时
    "retry_attempts": 5,      # 重试5次
    "retry_delay": 2.0        # 重试间隔2秒
}

# 2. 配置代理
proxy_config = {
    "http_proxy": "http://proxy:8080",
    "https_proxy": "https://proxy:8080",
    "no_proxy": "localhost,127.0.0.1"
}

# 3. 使用用户代理轮换
user_agents = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
]
```

#### Q10: 被网站反爬虫检测
**症状**: 返回403错误，验证码页面，或空白页面

**解决方案**:
```python
# 1. 模拟真实用户行为
behavior_config = {
    "random_delay": True,     # 随机延迟
    "mouse_movement": True,   # 模拟鼠标移动
    "scroll_behavior": True,  # 模拟滚动行为
    "typing_delay": 100       # 打字延迟
}

# 2. 轮换请求头
headers_rotation = {
    "user_agent_rotation": True,
    "accept_language_rotation": True,
    "accept_encoding_rotation": True
}

# 3. 使用代理池
proxy_pool = [
    "http://proxy1:8080",
    "http://proxy2:8080",
    "http://proxy3:8080"
]
```

### 数据问题

#### Q11: 数据提取不准确
**症状**: 提取的数据为空或不正确

**解决方案**:
```python
# 1. 检查选择器
def debug_selectors():
    # 使用多种选择器策略
    selectors = [
        "css:h1.title",
        "xpath://h1[@class='title']",
        "text:商品标题",
        "data-testid:product-title"
    ]
    
    for selector in selectors:
        try:
            element = page.locator(selector)
            if element.count() > 0:
                print(f"Found with {selector}: {element.text_content()}")
        except Exception as e:
            print(f"Failed with {selector}: {e}")

# 2. 等待元素加载
await page.wait_for_selector("h1.title", timeout=30000)
await page.wait_for_load_state("networkidle")

# 3. 处理动态内容
await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
await page.wait_for_timeout(2000)  # 等待内容加载
```

#### Q12: 数据格式不一致
**症状**: 同一字段的数据格式不统一

**解决方案**:
```python
# 数据清洗和标准化
def clean_data(raw_data):
    cleaned = {}
    
    # 价格标准化
    if 'price' in raw_data:
        price = raw_data['price']
        price = re.sub(r'[^\d.]', '', price)  # 移除非数字字符
        cleaned['price'] = float(price) if price else 0.0
    
    # 日期标准化
    if 'date' in raw_data:
        date_str = raw_data['date']
        try:
            cleaned['date'] = datetime.strptime(date_str, '%Y-%m-%d').isoformat()
        except ValueError:
            cleaned['date'] = None
    
    # 文本清理
    if 'description' in raw_data:
        desc = raw_data['description']
        desc = re.sub(r'\s+', ' ', desc)  # 标准化空白字符
        desc = desc.strip()
        cleaned['description'] = desc
    
    return cleaned
```

## 🔍 调试技巧

### 启用详细日志
```python
import logging

# 设置日志级别
logging.basicConfig(level=logging.DEBUG)

# 启用 Playwright 调试
import os
os.environ['DEBUG'] = 'pw:api'
```

### 使用浏览器开发者工具
```python
# 启用有头模式进行调试
browser = await playwright.chromium.launch(
    headless=False,
    devtools=True,  # 自动打开开发者工具
    slow_mo=1000    # 慢动作模式
)
```

### 截图调试
```python
# 在关键步骤截图
await page.screenshot(path="debug_step1.png")
await page.locator("button").click()
await page.screenshot(path="debug_step2.png")
```

## 📞 获取帮助

如果以上解决方案都无法解决您的问题：

1. **查看日志**: 检查 `logs/` 目录下的详细日志
2. **搜索Issues**: 在 [Git Issues](https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp/issues) 中搜索类似问题
3. **提交Issue**: 创建新的Issue，包含：
   - 详细的错误信息
   - 复现步骤
   - 系统环境信息
   - 相关日志文件
4. **社区讨论**: 提交 [合并请求](https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp/merge_requests)

### Issue模板
```markdown
## 问题描述
[简要描述遇到的问题]

## 复现步骤
1. [第一步]
2. [第二步]
3. [第三步]

## 预期行为
[描述您期望发生的情况]

## 实际行为
[描述实际发生的情况]

## 环境信息
- 操作系统: [Windows/macOS/Linux]
- Python版本: [3.9/3.10/3.11]
- 项目版本: [v1.0.0]

## 错误日志
```
[粘贴相关的错误日志]
```

## 附加信息
[任何其他相关信息]
```

---

*最后更新: 2025-01-29*
