import asyncio
from urllib.parse import urlparse

from mcp import ClientSession
from mcp.client.sse import sse_client
from mcp.client.streamable_http import streamablehttp_client

MCP_URL = "http://localhost:8931/mcp"       # 换成你的 /sse 或 /mcp

def choose_transport(url: str):
    """根据 URL 选择 SSE vs. Streamable‑HTTP，并返回 (ctx, unpack_len)."""
    if urlparse(url).path.endswith("/sse"):
        return sse_client(url), 2            # read, write
    return streamablehttp_client(url), 3     # read, write, close

async def main():
    ctx, n = choose_transport(MCP_URL)

    async with ctx as conn:
        # 动态解包
        read, write = conn[:2]               # 只取前两个即可

        async with ClientSession(read, write) as session:
            await session.initialize()       # 必须 handshake

            # === ① 列出全部可用工具 =========================
            tools = await session.list_tools()
            names = [t.name for t in tools.tools]
            print(f"🛠️  可用工具数：{len(names)}")
            print("   ", ", ".join(names), "\n")

            # === ② 浏览器示例操作 ===========================
            await session.call_tool(
                "browser_navigate",
                {"url": "https://google.com"}
            )
            await session.call_tool(
                "browser_snapshot",
                {"name": "example.png", "fullPage": True}
            )
            await session.call_tool("playwright_close", {})
            print("✅ screenshot saved → example.png")

if __name__ == "__main__":
    asyncio.run(main())
