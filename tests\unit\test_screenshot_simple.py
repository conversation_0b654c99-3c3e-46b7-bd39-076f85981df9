"""
Simple test script to verify screenshot saving functionality.
"""

import os
import sys
import time

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.core.browser import <PERSON><PERSON><PERSON>


def test_screenshot_saving():
    """Test screenshot saving with default path."""
    print("🧪 Testing screenshot saving functionality...")
    
    browser = Browser(headless=True)
    
    try:
        # Navigate to a test page
        print("🌐 Navigating to test page...")
        result = browser.navigate("https://httpbin.org/html")
        print(f"✅ Navigation result: {result}")
        
        # Take screenshot without specifying path (should use default)
        print("📸 Taking screenshot with default path...")
        screenshot_path = browser.screenshot()
        
        print(f"📁 Screenshot saved to: {screenshot_path}")
        print(f"📂 Full path: {os.path.abspath(screenshot_path)}")
        
        # Verify screenshot was saved
        if os.path.exists(screenshot_path):
            file_size = os.path.getsize(screenshot_path)
            print(f"✅ Screenshot file exists, size: {file_size} bytes")
        else:
            print(f"❌ Screenshot file not found: {screenshot_path}")
            return False
        
        # Take another screenshot to test unique naming
        print("📸 Taking second screenshot...")
        time.sleep(1)  # Ensure different timestamp
        screenshot_path2 = browser.screenshot()
        print(f"📁 Second screenshot saved to: {screenshot_path2}")
        
        if screenshot_path != screenshot_path2:
            print("✅ Screenshots have unique names")
        else:
            print("❌ Screenshots have same name")
            return False
        
        # Test full page screenshot
        print("📸 Taking full page screenshot...")
        screenshot_path3 = browser.screenshot(full_page=True)
        print(f"📁 Full page screenshot saved to: {screenshot_path3}")
        
        if os.path.exists(screenshot_path3):
            print("✅ Full page screenshot saved successfully")
        else:
            print("❌ Full page screenshot not found")
            return False
        
        print("\n📋 Summary:")
        print(f"  - Default screenshot: {screenshot_path}")
        print(f"  - Second screenshot: {screenshot_path2}")
        print(f"  - Full page screenshot: {screenshot_path3}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        browser.close()
        print("🔒 Browser closed")


def test_screenshots_directory():
    """Test that screenshots directory is created properly."""
    print("\n🧪 Testing screenshots directory creation...")
    
    # Check if screenshots directory exists
    screenshots_dir = "../../screenshots"
    if os.path.exists(screenshots_dir):
        print(f"📂 Screenshots directory exists: {os.path.abspath(screenshots_dir)}")
        
        # List files in directory
        files = os.listdir(screenshots_dir)
        if files:
            print(f"📁 Files in screenshots directory:")
            for file in files:
                file_path = os.path.join(screenshots_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"  - {file} ({file_size} bytes)")
        else:
            print("📁 Screenshots directory is empty")
    else:
        print(f"❌ Screenshots directory not found: {os.path.abspath(screenshots_dir)}")


if __name__ == "__main__":
    print("🚀 Starting screenshot saving tests...\n")
    
    # Test screenshot saving
    success = test_screenshot_saving()
    
    # Test directory
    test_screenshots_directory()
    
    if success:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
