# 用户手册

## 📋 概述

iICrawlerMCP是一个智能的网页爬虫平台，通过录制用户操作自动生成爬虫代码。本手册将详细介绍如何使用系统的各项功能。

## 🚀 开始使用

### 登录系统

1. 打开浏览器，访问 `http://localhost:8000`
2. 使用您的账户登录
3. 进入主控制台界面

### 主界面介绍

主界面包含以下几个区域：
- **导航栏**: 包含主要功能菜单
- **任务面板**: 创建和管理爬虫任务
- **监控面板**: 实时查看任务执行状态
- **结果面板**: 查看和下载爬取结果

## 📝 创建爬虫任务

### 方式一：录制模式（推荐）

#### 1. 启动录制会话
```
1. 点击"新建任务"按钮
2. 选择"录制模式"
3. 输入目标网站URL
4. 点击"开始录制"
```

#### 2. 操作演示
系统会打开一个新的浏览器窗口：
- 在浏览器中正常操作，如点击、输入、滚动等
- 系统会自动记录您的所有操作
- 操作完成后，点击"结束录制"

#### 3. 配置数据字段
录制完成后，系统会显示操作序列：
- 选择需要提取的数据字段
- 为每个字段设置名称和类型
- 预览提取结果

#### 4. 生成爬虫
- 点击"生成爬虫"按钮
- 系统会自动生成爬虫代码
- 可以预览和编辑生成的代码

### 方式二：模板模式

#### 1. 选择模板
```
1. 点击"新建任务"按钮
2. 选择"模板模式"
3. 从预设模板中选择合适的类型：
   - 电商商品信息
   - 新闻文章
   - 社交媒体内容
   - 表格数据
   - 自定义模板
```

#### 2. 配置参数
根据选择的模板，配置相应参数：
- **目标URL**: 要爬取的网站地址
- **数据字段**: 需要提取的信息类型
- **过滤条件**: 数据筛选规则
- **爬取数量**: 最大爬取条目数

#### 3. 高级设置
- **并发设置**: 同时运行的爬虫数量
- **延迟设置**: 请求间隔时间
- **重试设置**: 失败重试次数
- **代理设置**: 使用代理服务器

### 方式三：API模式

#### 1. 获取API密钥
```
1. 进入"设置"页面
2. 点击"API密钥"选项卡
3. 生成新的API密钥
4. 复制密钥用于API调用
```

#### 2. 使用API创建任务
```python
import requests

# 创建任务
task_data = {
    "name": "我的爬虫任务",
    "target_url": "https://example.com",
    "data_fields": ["title", "content", "price"],
    "max_items": 100
}

response = requests.post(
    "http://localhost:8000/api/tasks",
    json=task_data,
    headers={"Authorization": f"Bearer {api_key}"}
)

task_id = response.json()["task_id"]
```

## 📊 监控任务执行

### 实时监控

#### 任务状态
任务执行过程中会显示以下状态：
- **准备中**: 正在初始化任务
- **录制中**: 正在录制用户操作
- **生成中**: 正在生成爬虫代码
- **执行中**: 正在执行爬虫任务
- **完成**: 任务执行完成
- **失败**: 任务执行失败

#### 进度指示器
- **总体进度**: 显示任务完成百分比
- **当前阶段**: 显示正在执行的具体步骤
- **已处理项目**: 显示已爬取的数据条目数
- **预估剩余时间**: 根据当前速度估算完成时间

#### 实时日志
- 查看详细的执行日志
- 过滤不同级别的日志信息
- 搜索特定的日志内容

### 性能监控

#### 系统资源
- **CPU使用率**: 当前CPU占用情况
- **内存使用率**: 内存占用情况
- **网络流量**: 网络请求统计
- **存储空间**: 磁盘使用情况

#### 任务统计
- **成功率**: 任务成功完成的比例
- **平均执行时间**: 任务平均耗时
- **错误统计**: 常见错误类型和频率
- **吞吐量**: 每分钟处理的数据量

## 📥 管理结果数据

### 查看结果

#### 在线预览
- 在结果面板中查看爬取的数据
- 支持表格、卡片、JSON等多种显示格式
- 可以搜索、排序和过滤数据

#### 数据统计
- 查看数据总量和分布情况
- 生成数据质量报告
- 检查重复和缺失数据

### 导出数据

#### 支持格式
- **JSON**: 结构化数据格式
- **CSV**: 表格数据格式
- **Excel**: 电子表格格式
- **XML**: 标记语言格式
- **PDF**: 报告格式

#### 导出选项
```
1. 选择要导出的数据范围
2. 选择导出格式
3. 配置导出选项：
   - 包含字段选择
   - 数据过滤条件
   - 排序方式
4. 点击"导出"按钮
5. 下载生成的文件
```

### 数据处理

#### 数据清洗
- 自动去除重复数据
- 标准化数据格式
- 处理缺失值
- 验证数据完整性

#### 数据转换
- 字段类型转换
- 数据格式标准化
- 计算衍生字段
- 数据聚合统计

## ⚙️ 系统设置

### 基本设置

#### 账户设置
- 修改个人信息
- 更改密码
- 设置头像
- 配置通知偏好

#### 界面设置
- 选择主题（明亮/暗黑）
- 设置语言
- 自定义布局
- 配置快捷键

### 高级设置

#### 爬虫配置
```
浏览器设置:
- 用户代理字符串
- 窗口大小
- 超时时间
- 代理服务器

性能设置:
- 最大并发数
- 请求延迟
- 重试次数
- 缓存策略

安全设置:
- IP白名单
- 访问频率限制
- 数据加密
- 审计日志
```

#### 通知设置
- 邮件通知配置
- 短信通知设置
- Webhook集成
- 钉钉/企业微信通知

## 🔧 故障排除

### 常见问题

#### 录制失败
**问题**: 录制过程中浏览器崩溃或无响应
**解决方案**:
1. 检查系统资源是否充足
2. 关闭其他占用资源的程序
3. 重启录制会话
4. 联系技术支持

#### 数据提取不准确
**问题**: 爬取的数据为空或格式错误
**解决方案**:
1. 检查目标网站是否有变化
2. 重新录制操作序列
3. 调整数据字段配置
4. 使用手动模式修正

#### 任务执行缓慢
**问题**: 爬虫执行速度过慢
**解决方案**:
1. 减少并发数量
2. 增加请求延迟
3. 检查网络连接
4. 优化选择器策略

### 获取帮助

#### 在线帮助
- 查看帮助文档
- 观看视频教程
- 参考示例项目
- 搜索常见问题

#### 技术支持
- 提交工单
- 在线客服
- 电话支持
- 邮件咨询

#### 社区支持
- 用户论坛
- QQ群交流
- 微信群讨论
- GitHub Issues

## 📚 最佳实践

### 录制技巧

#### 操作规范
1. **慢速操作**: 录制时动作要慢，确保系统能准确捕获
2. **清晰路径**: 选择最直接的操作路径
3. **等待加载**: 等待页面完全加载后再进行下一步操作
4. **避免干扰**: 录制期间不要进行无关操作

#### 数据选择
1. **精确选择**: 选择最具代表性的数据字段
2. **类型匹配**: 确保字段类型与实际数据匹配
3. **命名规范**: 使用有意义的字段名称
4. **验证结果**: 录制后验证提取结果的准确性

### 性能优化

#### 任务配置
1. **合理并发**: 根据目标网站性能设置并发数
2. **适当延迟**: 设置合理的请求间隔避免被封
3. **错误处理**: 配置重试机制处理临时错误
4. **资源监控**: 监控系统资源使用情况

#### 数据管理
1. **定期清理**: 定期清理过期的任务和数据
2. **备份重要数据**: 对重要数据进行备份
3. **监控存储空间**: 避免存储空间不足
4. **数据归档**: 对历史数据进行归档处理

---

*最后更新: 2025-01-29*
