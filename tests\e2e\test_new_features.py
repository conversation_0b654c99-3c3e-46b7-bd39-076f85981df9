#!/usr/bin/env python3
"""
End-to-end tests for browser automation features.

This module contains end-to-end tests that test complete workflows
and user scenarios with the browser automation tools.
"""

import pytest
import logging

from iicrawlermcp.agents.crawler_agent import build_agent
from iicrawlermcp.tools.browser_tools import (
    navigate, browser_snapshot, browser_click, browser_type,
    browser_wait_for, browser_evaluate, browser_hover,
    browser_press_key, browser_select_option, screenshot
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestBasicWorkflows:
    """Test class for basic browser automation workflows."""
    
    def test_basic_navigation(self):
        """Test basic navigation functionality."""
        try:
            result = navigate.invoke({"url": "https://httpbin.org/html"})
            assert "✅" in result
            assert "httpbin.org" in result
        except Exception as e:
            pytest.skip(f"Navigation test skipped: {e}")
    
    def test_snapshot_functionality(self):
        """Test page snapshot functionality."""
        try:
            # First navigate to a page
            navigate.invoke({"url": "https://httpbin.org/html"})
            
            result = browser_snapshot.invoke({})
            assert result is not None
            assert isinstance(result, str)
        except Exception as e:
            pytest.skip(f"Snapshot test skipped: {e}")
    
    def test_screenshot_with_new_params(self):
        """Test screenshot with new standard parameters."""
        try:
            # First navigate to a page
            navigate.invoke({"url": "https://httpbin.org/html"})
            
            # Test basic screenshot
            result1 = screenshot.invoke({"filename": "screenshots/test_basic.png"})
            assert "📸" in result1
            
            # Test raw PNG screenshot
            result2 = screenshot.invoke({"filename": "screenshots/test_raw.png", "raw": True})
            assert "📸" in result2
            
        except Exception as e:
            pytest.skip(f"Screenshot test skipped: {e}")


class TestJavaScriptIntegration:
    """Test class for JavaScript evaluation functionality."""
    
    def test_javascript_evaluation(self):
        """Test JavaScript evaluation functionality."""
        try:
            # First navigate to a page
            navigate.invoke({"url": "https://httpbin.org/html"})
            
            # Test page-level JavaScript
            result = browser_evaluate.invoke({"function": "() => document.title"})
            assert result is not None
        except Exception as e:
            pytest.skip(f"JavaScript evaluation test skipped: {e}")


class TestInteractionWorkflows:
    """Test class for user interaction workflows."""
    
    def test_wait_functionality(self):
        """Test wait functionality."""
        try:
            result = browser_wait_for.invoke({"time": 1.0})
            assert "⏱️" in result or "waited" in result.lower()
        except Exception as e:
            pytest.skip(f"Wait test skipped: {e}")
    
    def test_key_press(self):
        """Test key press functionality."""
        try:
            result = browser_press_key.invoke({"key": "Escape"})
            assert result is not None
        except Exception as e:
            pytest.skip(f"Key press test skipped: {e}")


class TestAgentIntegration:
    """Test class for agent integration workflows."""
    
    def test_agent_integration(self):
        """Test that the new tools work with the LangChain agent."""
        try:
            agent = build_agent()
            
            # Test a simple task that uses multiple tools
            task = "Navigate to https://httpbin.org/html and take a screenshot"
            result = agent.invoke(task)
            
            assert "output" in result
            assert result["output"] is not None
            
            # Clean up
            agent.cleanup()
            
        except Exception as e:
            pytest.skip(f"Agent integration test skipped: {e}")
    
    def test_complex_workflow(self):
        """Test a complex workflow with multiple steps."""
        try:
            agent = build_agent()
            
            # Test a more complex task
            task = """
            Navigate to https://www.google.com.hk/,
            take a screenshot,
            and search hotel,return first title of results
            """
            result = agent.invoke(task)
            print(result)
            assert "output" in result
            assert result["output"] is not None
            
            # Clean up
            agent.cleanup()
            
        except Exception as e:
            pytest.skip(f"Complex workflow test skipped: {e}")


def run_all_tests():
    """
    Standalone function to run all tests.
    
    This function can be run independently to test all functionality
    without using pytest.
    """
    print("Starting comprehensive test of new browser automation features...")
    
    test_functions = [
        ("Basic Navigation", lambda: navigate.invoke({"url": "https://httpbin.org/html"})),
        ("Page Snapshot", lambda: browser_snapshot.invoke({})),
        ("Enhanced Screenshot", lambda: screenshot.invoke({"filename": "screenshots/test_basic.png"})),
        ("JavaScript Evaluation", lambda: browser_evaluate.invoke({"function": "() => document.title"})),
        ("Wait Functionality", lambda: browser_wait_for.invoke({"time": 1.0})),
        ("Key Press", lambda: browser_press_key.invoke({"key": "Escape"})),
    ]
    
    results = {}
    
    for test_name, test_func in test_functions:
        try:
            print(f"\n=== Testing {test_name} ===")
            result = test_func()
            print(f"{test_name} result: {result}")
            results[test_name] = True
        except Exception as e:
            print(f"{test_name} failed: {e}")
            results[test_name] = False
    
    # Test agent integration
    try:
        print("\n=== Testing Agent Integration ===")
        agent = build_agent()
        task = "Navigate to https://httpbin.org/html and take a screenshot"
        result = agent.invoke(task)
        print(f"Agent task result: {result.get('output', 'No output')}")
        agent.cleanup()
        results["Agent Integration"] = True
    except Exception as e:
        print(f"Agent integration failed: {e}")
        results["Agent Integration"] = False
    
    # Print summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    # Cleanup
    try:
        from iicrawlermcp.tools import cleanup_tools
        cleanup_tools()
        print("✅ Cleanup completed")
    except Exception as e:
        print(f"⚠️ Cleanup warning: {e}")
    
    return passed == total


if __name__ == "__main__":
    import sys
    success = run_all_tests()
    sys.exit(0 if success else 1)
