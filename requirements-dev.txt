# Development dependencies for iICrawlerMCP
# Install with: pip install -r requirements-dev.txt

# Include core dependencies
-r requirements.txt

# Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0

# Code formatting and linting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# Type checking
mypy>=1.0.0
types-requests>=2.31.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Development tools
pre-commit>=3.0.0
tox>=4.0.0
