#!/usr/bin/env python3
"""
QQEng英文学习站点研究示例

这个示例演示了如何使用CrawlerAgent自动完成英文学习站点的信息收集：
1. 导航到 https://qqeng.net/
2. 了解网站的英文学习服务
3. 查找报名方式和流程
4. 了解上课方式和地点
5. 截图保存重要信息

使用最简单的方法：让CrawlerAgent自动选择合适的Agent和工具。
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def qqeng_learning_research():
    """
    研究QQEng英文学习站点的报名和上课信息
    """
    logger.info("开始QQEng英文学习站点研究...")
    
    # 配置参数
    config.HEADLESS = False  # 显示浏览器窗口以便观察
    config.BROWSER_TIMEOUT = 30000  # 30秒超时
    config.DOM_HIGHLIGHT_ENABLED = True  # 启用高亮
    config.DOM_HIGHLIGHT_BORDER_WIDTH = 3  # 高亮边框宽度
    
    # 创建CrawlerAgent
    agent = build_agent('crawler')
    
    try:
        # 步骤验证函数
        def verify_step_completion(step_name, result, expected_keywords=None, max_retries=2):
            """验证步骤是否成功完成"""
            output = result.get('output', str(result))

            # 检查是否有明显的错误信息
            error_indicators = ['错误', 'error', '失败', 'failed', '未找到', 'not found', '无法', 'cannot']
            if any(indicator in output.lower() for indicator in error_indicators):
                logger.warning(f"{step_name} 可能存在问题: {output}")
                return False, f"检测到错误信息: {output}"

            # 检查是否包含期望的关键词
            if expected_keywords:
                found_keywords = [kw for kw in expected_keywords if kw in output]
                if not found_keywords:
                    logger.warning(f"{step_name} 未找到期望内容: {expected_keywords}")
                    return False, f"未找到期望关键词: {expected_keywords}"

            logger.info(f"{step_name} 验证通过")
            return True, "验证成功"

        def verify_page_state(expected_elements=None):
            """验证当前页面状态"""
            try:
                # 使用ElementAgent验证页面元素
                page_check = agent.invoke("""
                使用smart_element_finder工具执行以下验证：
                1. 获取当前页面的基本信息（URL、标题）
                2. 检查页面是否完全加载
                3. 获取页面上的主要可见元素
                请返回详细的页面状态信息
                """)

                if expected_elements:
                    element_check = agent.invoke(f"""
                    使用smart_element_finder工具检查页面上是否存在以下元素：
                    {', '.join(expected_elements)}

                    对每个元素，请说明：
                    - 是否找到
                    - 元素位置和状态
                    - 是否可交互
                    """)
                    return page_check, element_check

                return page_check, None
            except Exception as e:
                logger.error(f"页面状态验证失败: {e}")
                return None, None

        def robust_task_execution(task_description, step_name, expected_keywords=None, max_retries=3):
            """健壮的任务执行，支持重试和验证"""
            for attempt in range(max_retries):
                try:
                    logger.info(f"{step_name} - 第{attempt+1}次尝试...")

                    # 执行任务
                    result = agent.invoke(task_description)

                    # 验证结果
                    success, msg = verify_step_completion(step_name, result, expected_keywords)

                    if success:
                        logger.info(f"{step_name} 执行成功: {result.get('output', str(result))}")
                        return result
                    else:
                        if attempt < max_retries - 1:
                            logger.warning(f"{step_name} 第{attempt+1}次尝试未完全成功，重试中... 原因: {msg}")
                            # 等待一下再重试
                            import time
                            time.sleep(2)
                            continue
                        else:
                            logger.error(f"{step_name} 多次尝试后仍未成功: {msg}")
                            return result  # 返回最后一次的结果，让调用者决定如何处理

                except Exception as e:
                    if attempt < max_retries - 1:
                        logger.warning(f"{step_name} 第{attempt+1}次尝试出错: {e}，重试中...")
                        import time
                        time.sleep(2)
                    else:
                        logger.error(f"{step_name} 所有尝试都失败了: {e}")
                        raise

            return None

        # 步骤1: 导航到QQEng网站
        logger.info("步骤1: 导航到QQEng网站...")
        result = robust_task_execution(
            "导航到 https://qqeng.net/ 网站",
            "导航到QQEng网站",
            expected_keywords=["成功", "完成", "qqeng"]
        )

        # 验证页面加载
        page_state, _ = verify_page_state(["英文", "学习", "课程"])
        if not page_state:
            logger.warning("页面状态验证失败，但继续执行...")

        logger.info(f"导航结果: {result['output']}")

        # 步骤2: 了解网站主要服务
        logger.info("步骤2: 了解网站主要服务...")
        result = robust_task_execution(
            "请提取这个英文学习网站的主要服务内容，包括：" +
            "1. 提供什么类型的英文学习服务" +
            "2. 主要的课程类型" +
            "3. 网站的特色和优势",
            "了解网站主要服务",
            expected_keywords=["英文", "学习", "课程", "服务"]
        )
        logger.info(f"网站服务信息: {result['output']}")

        # 步骤3: 查找报名相关信息
        logger.info("步骤3: 查找报名相关信息...")
        result = robust_task_execution(
            "请查找并提取关于报名的信息，包括：" +
            "1. 如何报名或注册" +
            "2. 报名流程是什么" +
            "3. 是否需要填写表单" +
            "4. 联系方式（电话、邮箱等）" +
            "5. 报名费用或价格信息",
            "查找报名相关信息",
            expected_keywords=["报名", "注册", "联系", "费用"]
        )
        logger.info(f"报名信息: {result['output']}")

        # 步骤4: 查找上课方式和地点信息
        logger.info("步骤4: 查找上课方式和地点信息...")
        result = robust_task_execution(
            "请查找并提取关于上课的信息，包括：" +
            "1. 上课方式（在线、线下、混合）" +
            "2. 上课地点或平台" +
            "3. 上课时间安排" +
            "4. 师资介绍" +
            "5. 课程安排和时长",
            "查找上课方式和地点信息",
            expected_keywords=["上课", "时间", "老师", "师资"]
        )
        logger.info(f"上课信息: {result['output']}")

        # 步骤5: 寻找更多详细信息的链接
        logger.info("步骤5: 寻找更多详细信息...")
        result = robust_task_execution(
            "请查找页面上是否有以下相关链接或按钮：" +
            "1. 课程介绍或详情页面" +
            "2. 免费试听或体验课" +
            "3. 师资介绍页面" +
            "4. 学员评价或案例" +
            "5. 常见问题FAQ",
            "寻找更多详细信息的链接",
            expected_keywords=["链接", "按钮", "页面"]
        )
        logger.info(f"相关链接信息: {result['output']}")

        # 步骤6: 尝试点击重要链接获取更多信息
        logger.info("步骤6: 尝试获取更多详细信息...")

        # 尝试点击课程相关链接
        try:
            # 先验证链接是否存在
            link_verification = agent.invoke("""
            使用smart_element_finder工具验证页面上的课程相关链接：
            1. 查找包含"课程"、"介绍"、"详情"等文字的链接
            2. 检查这些链接是否可点击
            3. 返回最合适的链接信息
            """)

            success, msg = verify_step_completion("链接验证", link_verification, ["链接", "课程"])

            if success:
                result = robust_task_execution(
                    "点击课程介绍或课程详情相关的链接",
                    "点击课程链接",
                    expected_keywords=["点击", "成功", "页面"]
                )
                logger.info(f"点击课程链接结果: {result['output']}")

                # 验证页面跳转
                page_state, _ = verify_page_state(["课程", "详情", "介绍"])

                # 提取课程详情页面信息
                result = robust_task_execution(
                    "请提取当前页面的课程详细信息，包括：" +
                    "1. 具体的课程类型和级别" +
                    "2. 课程内容和教学方法" +
                    "3. 学习周期和进度安排" +
                    "4. 学费和付费方式",
                    "提取课程详细信息",
                    expected_keywords=["课程", "内容", "学费"]
                )
                logger.info(f"课程详情: {result['output']}")
            else:
                logger.warning(f"未找到可点击的课程链接: {msg}")

        except Exception as e:
            logger.warning(f"点击课程链接失败: {e}")

        # 步骤7: 查找联系方式
        logger.info("步骤7: 查找详细联系方式...")
        result = robust_task_execution(
            "请仔细查找页面上的所有联系方式，包括：" +
            "1. 客服电话号码" +
            "2. 微信号或QQ号" +
            "3. 邮箱地址" +
            "4. 在线客服入口" +
            "5. 公司地址" +
            "6. 工作时间",
            "查找详细联系方式",
            expected_keywords=["电话", "微信", "邮箱", "联系"]
        )
        logger.info(f"联系方式: {result['output']}")

        # 步骤8: 最终截图保存
        logger.info("步骤8: 保存最终截图...")
        result = robust_task_execution(
            "截取当前页面的截图，保存为 qqeng_final_info",
            "保存最终截图",
            expected_keywords=["截图", "保存", "成功"]
        )
        logger.info(f"截图保存结果: {result['output']}")

        logger.info("QQEng英文学习站点研究完成！")
        
        # 总结信息
        print("\n" + "="*60)
        print("QQEng英文学习站点研究总结")
        print("="*60)
        print("✅ 已完成网站基本信息收集")
        print("✅ 已查找报名相关信息")
        print("✅ 已了解上课方式和安排")
        print("✅ 已收集联系方式")
        print("✅ 已保存重要页面截图")
        print("\n请查看日志输出和screenshots/examples目录中的截图文件")
        print("获取更多详细信息。")
        
    except Exception as e:
        logger.error(f"研究过程中出现错误: {e}")
        print(f"\n❌ 研究过程中出现错误: {e}")
        
    finally:
        # 清理资源
        try:
            agent.cleanup()
        except:
            pass


def main():
    """主函数"""
    print("QQEng英文学习站点研究示例")
    print("=" * 40)
    print("这个示例将帮助您了解QQEng网站的：")
    print("• 英文学习服务内容")
    print("• 报名方式和流程")
    print("• 上课方式和地点")
    print("• 联系方式和详细信息")
    print("=" * 40)
    
    try:
        qqeng_learning_research()
    except KeyboardInterrupt:
        print("\n用户中断了程序执行")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        logger.error(f"程序执行出错: {e}", exc_info=True)


if __name__ == "__main__":
    main()
