#!/usr/bin/env python3
"""
测试委托工具的实现

这个脚本测试新的委托工具是否能让CrawlerAgent自动调用ElementAgent
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_delegation_tools():
    """
    测试CrawlerAgent是否能自动使用委托工具
    """
    
    print("🧪 测试委托工具实现")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 检查工具列表
        print(f"\n🔧 CrawlerAgent工具数量: {len(crawler_agent.tools)}")
        tool_names = [tool.name for tool in crawler_agent.tools]
        print("📋 可用工具:")
        for tool_name in tool_names:
            print(f"  • {tool_name}")
        
        # 检查是否包含委托工具
        delegation_tools = [
            'smart_element_finder',
            'smart_browser_action_finder'
        ]
        
        print(f"\n🔍 检查委托工具:")
        for tool_name in delegation_tools:
            if tool_name in tool_names:
                print(f"  ✅ {tool_name}")
            else:
                print(f"  ❌ {tool_name} - 缺失")
        
        # 测试任务：需要元素分析的场景
        test_task = """
        我需要研究Google搜索页面的结构，请按以下步骤执行：
        
        1. 导航到 https://www.google.com
        2. 分析页面上有哪些可交互的元素
        3. 特别关注搜索框和搜索按钮
        4. 提供页面元素的详细信息
        
        请自动选择合适的工具和方法来完成这个任务。
        """
        
        print(f"\n🚀 执行测试任务...")
        print("📋 任务描述：分析Google搜索页面结构")
        print("🎯 期望：CrawlerAgent应该自动调用委托工具")
        
        # 执行任务
        result = crawler_agent.invoke(test_task)
        
        print("\n" + "="*50)
        print("🎉 任务执行完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Delegation test error: {e}")
        return None


def test_individual_tools():
    """
    测试单个委托工具
    """
    print("\n🔧 测试单个委托工具")
    print("-" * 30)
    
    try:
        from iicrawlermcp.tools.delegation_tools import (
            smart_element_finder,
            smart_browser_action_finder
        )
        
        print("✅ 委托工具导入成功")
        
        # 测试工具描述
        print(f"\n📝 smart_element_finder 描述:")
        print(f"   {smart_element_finder.description}")

        print(f"\n📝 smart_browser_action_finder 描述:")
        print(f"   {smart_browser_action_finder.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 单个工具测试失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🧪 委托工具实现测试")
    print("=" * 60)
    print("这个测试将验证：")
    print("• 🔧 委托工具是否正确创建")
    print("• 🤖 CrawlerAgent是否包含委托工具")
    print("• 🎯 LLM是否能看到并调用委托工具")
    print("• 🔄 委托机制是否正常工作")
    print("=" * 60)
    
    # 测试单个工具
    if not test_individual_tools():
        print("\n❌ 单个工具测试失败，停止测试")
        return 1
    
    # 询问用户是否继续完整测试
    user_input = input("\n是否继续完整的委托测试？(y/n): ").strip().lower()
    if user_input not in ['y', 'yes', '是', '好']:
        print("👋 测试已取消")
        return 0
    
    # 执行完整测试
    result = test_delegation_tools()
    
    if result:
        print("\n🎉 委托工具测试完成！")
        print("\n💡 关键改进：")
        print("• ✅ 委托工具已添加到CrawlerAgent")
        print("• ✅ LLM现在可以看到委托选项")
        print("• ✅ 自动选择合适的专门Agent")
        print("• ✅ DOM工具返回信息已优化")
        print("• ✅ 智能元素查找功能增强")
        
        return 0
    else:
        print("\n❌ 委托工具测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
