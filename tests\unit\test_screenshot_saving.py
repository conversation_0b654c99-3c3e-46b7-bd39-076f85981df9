"""
Unit tests for screenshot saving functionality.

This module tests the default screenshot saving mechanism and verifies
that screenshots are saved to the correct directory with proper naming.
"""

import pytest
import os
import sys
import glob
import time
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.core.browser import Browser
from iicrawlermcp.tools.browser_tools import screenshot


class TestScreenshotSaving:
    """Test class for screenshot saving functionality."""
    
    def setup_method(self):
        """Setup method called before each test."""
        # Clean up any existing test screenshots
        self.cleanup_test_screenshots()
    
    def teardown_method(self):
        """Teardown method called after each test."""
        # Clean up test screenshots
        self.cleanup_test_screenshots()
    
    def cleanup_test_screenshots(self):
        """Clean up test screenshots from the screenshots directory."""
        screenshot_patterns = [
            "screenshots/screenshot_*.png",
            "screenshots/test_*.png",
            "D:/Users/<USER>/PycharmProjects/iICrawlerMCP/screenshots/screenshot_*.png",
            "D:/Users/<USER>/PycharmProjects/iICrawlerMCP/screenshots/test_*.png"
        ]
        
        for pattern in screenshot_patterns:
            for file_path in glob.glob(pattern):
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"Cleaned up: {file_path}")
                except Exception as e:
                    print(f"Failed to clean up {file_path}: {e}")
    
    def test_browser_default_screenshot_saving(self):
        """Test that browser saves screenshots to default location when no path is specified."""
        browser = Browser(headless=True)
        
        try:
            # Navigate to a test page
            result = browser.navigate("https://httpbin.org/html")
            assert result['success'] is True
            
            # Take screenshot without specifying path (should use default)
            screenshot_path = browser.screenshot()
            
            # Verify screenshot was saved
            assert screenshot_path is not None
            assert isinstance(screenshot_path, str)
            assert screenshot_path.startswith("screenshots/screenshot_")
            assert screenshot_path.endswith(".png")
            assert os.path.exists(screenshot_path)
            assert os.path.getsize(screenshot_path) > 0
            
            print(f"✅ Default screenshot saved to: {screenshot_path}")
            
            # Verify the timestamp format in filename
            filename = os.path.basename(screenshot_path)
            # Should be like: screenshot_20231201_143022.png
            assert filename.startswith("screenshot_")
            timestamp_part = filename.replace("screenshot_", "").replace(".png", "")
            # Verify timestamp format: YYYYMMDD_HHMMSS
            assert len(timestamp_part) == 15  # YYYYMMDD_HHMMSS
            assert timestamp_part[8] == "_"  # underscore separator
            
        finally:
            browser.close()
    
    def test_browser_full_page_screenshot_saving(self):
        """Test full page screenshot saving with default path."""
        browser = Browser(headless=True)
        
        try:
            # Navigate to a page with content
            result = browser.navigate("https://httpbin.org/html")
            assert result['success'] is True
            
            # Take full page screenshot without specifying path
            screenshot_path = browser.screenshot(full_page=True)
            
            # Verify screenshot was saved
            assert screenshot_path is not None
            assert os.path.exists(screenshot_path)
            assert os.path.getsize(screenshot_path) > 0
            
            print(f"✅ Full page screenshot saved to: {screenshot_path}")
            
        finally:
            browser.close()
    
    def test_tool_screenshot_default_saving(self):
        """Test the LangChain tool screenshot function with default saving."""
        from iicrawlermcp.core.browser import get_global_browser, close_global_browser

        try:
            # Get global browser and navigate
            browser = get_global_browser()
            browser.navigate("https://httpbin.org/html")

            # Use the tool function with proper LangChain invoke method
            result = screenshot.invoke({})  # Empty dict for no parameters

            # Verify the result message
            assert isinstance(result, str)
            assert "📸 Screenshot saved:" in result
            assert ".png" in result

            # Extract the path from the result message
            # Format: "📸 Screenshot saved: screenshots/screenshot_YYYYMMDD_HHMMSS.png"
            path_start = result.find("screenshots/")
            screenshot_path = result[path_start:]

            # Verify the file exists
            assert os.path.exists(screenshot_path)
            assert os.path.getsize(screenshot_path) > 0

            print(f"✅ Tool screenshot saved to: {screenshot_path}")

        finally:
            close_global_browser()
    
    def test_multiple_screenshots_unique_names(self):
        """Test that multiple screenshots get unique names based on timestamp."""
        browser = Browser(headless=True)
        
        try:
            # Navigate to a test page
            browser.navigate("https://httpbin.org/html")
            
            # Take multiple screenshots with small delay
            screenshot_paths = []
            for i in range(3):
                screenshot_path = browser.screenshot()
                screenshot_paths.append(screenshot_path)
                time.sleep(1)  # Ensure different timestamps
            
            # Verify all screenshots have unique names
            assert len(set(screenshot_paths)) == 3, "All screenshots should have unique names"
            
            # Verify all files exist
            for path in screenshot_paths:
                assert os.path.exists(path)
                assert os.path.getsize(path) > 0
                print(f"✅ Screenshot saved: {path}")
            
        finally:
            browser.close()
    
    def test_screenshots_directory_creation(self):
        """Test that screenshots directory is created if it doesn't exist."""
        # Remove screenshots directory if it exists
        if os.path.exists("screenshots"):
            import shutil
            shutil.rmtree("screenshots")
        
        browser = Browser(headless=True)
        
        try:
            # Navigate and take screenshot
            browser.navigate("https://httpbin.org/html")
            screenshot_path = browser.screenshot()
            
            # Verify directory was created
            assert os.path.exists("screenshots")
            assert os.path.isdir("screenshots")
            assert os.path.exists(screenshot_path)
            
            print(f"✅ Screenshots directory created and screenshot saved: {screenshot_path}")
            
        finally:
            browser.close()
    
    def test_screenshot_with_custom_path_vs_default(self):
        """Test difference between custom path and default path behavior."""
        browser = Browser(headless=True)
        
        try:
            browser.navigate("https://httpbin.org/html")
            
            # Test with custom path
            custom_path = "screenshots/test_custom.png"
            result_custom = browser.screenshot(custom_path)
            assert result_custom == custom_path
            assert os.path.exists(custom_path)
            
            # Test with default path
            result_default = browser.screenshot()
            assert result_default != custom_path
            assert result_default.startswith("screenshots/screenshot_")
            assert os.path.exists(result_default)
            
            print(f"✅ Custom path: {result_custom}")
            print(f"✅ Default path: {result_default}")
            
        finally:
            browser.close()


if __name__ == "__main__":
    # Run screenshot saving tests
    print("Testing screenshot saving functionality...")
    
    test_instance = TestScreenshotSaving()
    
    try:
        test_instance.setup_method()
        
        print("\n1. Testing default screenshot saving...")
        test_instance.test_browser_default_screenshot_saving()
        
        print("\n2. Testing full page screenshot saving...")
        test_instance.test_browser_full_page_screenshot_saving()
        
        print("\n3. Testing tool screenshot saving...")
        test_instance.test_tool_screenshot_default_saving()
        
        print("\n4. Testing multiple screenshots...")
        test_instance.test_multiple_screenshots_unique_names()
        
        print("\n5. Testing directory creation...")
        test_instance.test_screenshots_directory_creation()
        
        print("\n6. Testing custom vs default paths...")
        test_instance.test_screenshot_with_custom_path_vs_default()
        
        print("\n✅ All screenshot saving tests passed!")
        
    except Exception as e:
        print(f"❌ Screenshot saving test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        test_instance.teardown_method()
