"""
Unit tests for browser functionality.

This module provides unit tests for the Browser class and related functionality.
"""

import pytest
import os
import tempfile

from iicrawlermcp.core.browser import Browser


class TestBrowser:
    """Test class for Browser functionality."""
    
    def test_browser_creation(self):
        """Test that <PERSON><PERSON><PERSON> can be created successfully."""
        browser = Browser()
        assert browser is not None
        # <PERSON>rowser defaults to headless=True
        assert isinstance(browser.headless, bool)
        assert browser.browser_type == "chromium"  # default browser type
        browser.close()
    
    def test_browser_navigation(self):
        """Test browser navigation functionality."""
        browser = Browser(headless=True)

        try:
            # Test navigation to a simple page
            result = browser.navigate("https://httpbin.org/html")
            assert isinstance(result, dict)
            assert result['success'] is True
            assert "httpbin.org" in result['url']

            # Test getting page info
            page_info = browser.get_page_info()
            assert isinstance(page_info, dict)
            assert 'title' in page_info
            assert 'url' in page_info
            assert "httpbin.org" in page_info['url']

        finally:
            browser.close()
    
    def test_browser_screenshot(self):
        """Test browser screenshot functionality."""
        browser = Browser(headless=True)

        try:
            # Navigate to a page first
            browser.navigate("https://httpbin.org/html")

            # Take screenshot with temporary file
            with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
                screenshot_path = tmp.name

            try:
                result = browser.screenshot(screenshot_path)
                assert result == screenshot_path  # screenshot() returns the path
                assert os.path.exists(screenshot_path)
                assert os.path.getsize(screenshot_path) > 0

            finally:
                # Clean up screenshot file
                if os.path.exists(screenshot_path):
                    os.unlink(screenshot_path)

        finally:
            browser.close()
    
    def test_browser_context_manager(self):
        """Test browser as context manager."""
        with Browser(headless=True) as browser:
            result = browser.navigate("https://httpbin.org/html")
            assert isinstance(result, dict)
            assert result['success'] is True

        # Browser should be closed automatically
        assert not browser._is_initialized
    
    def test_browser_error_handling(self):
        """Test browser error handling."""
        browser = Browser(headless=True)
        
        try:
            # Test screenshot without navigation should fail
            with pytest.raises(Exception):
                browser.screenshot()
            
            # Test navigation to invalid URL should fail
            with pytest.raises(Exception):
                browser.navigate("invalid-url")
                
        finally:
            browser.close()


class TestBrowserConfiguration:
    """Test browser configuration options."""
    
    def test_headless_mode(self):
        """Test browser headless mode configuration."""
        # Test headless=True
        browser_headless = Browser(headless=True)
        assert browser_headless.headless is True
        browser_headless.close()
        
        # Test headless=False
        browser_headed = Browser(headless=False)
        assert browser_headed.headless is False
        browser_headed.close()
    
    def test_browser_type_configuration(self):
        """Test browser type configuration."""
        # Test different browser types
        browser_chromium = Browser(browser_type="chromium")
        assert browser_chromium.browser_type == "chromium"
        browser_chromium.close()

        browser_firefox = Browser(browser_type="firefox")
        assert browser_firefox.browser_type == "firefox"
        browser_firefox.close()

    def test_default_configuration(self):
        """Test browser with default configuration."""
        browser = Browser()
        assert browser.headless is True  # default is True
        assert browser.browser_type == "chromium"  # default browser type
        browser.close()


if __name__ == "__main__":
    # Run basic browser test
    print("Testing browser functionality...")
    
    try:
        browser = Browser(headless=True)
        print("✅ Browser created successfully")
        
        result = browser.navigate("https://httpbin.org/html")
        print(f"✅ Navigation: {result}")

        page_info = browser.get_page_info()
        print(f"✅ Page info: {page_info}")

        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
            screenshot_path = tmp.name

        result = browser.screenshot(screenshot_path)
        print(f"✅ Screenshot: {result}")
        
        browser.close()
        print("✅ Browser closed successfully")
        
        # Clean up
        if os.path.exists(screenshot_path):
            os.unlink(screenshot_path)
        
        print("\n✅ All browser tests passed!")
        
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        import traceback
        traceback.print_exc()
