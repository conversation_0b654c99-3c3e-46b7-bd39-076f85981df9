# 配置指南

## 📋 概述

本文档详细介绍iICrawlerMCP系统的各项配置选项，包括环境配置、性能调优、安全设置等。

## 🔧 环境配置

### 基础环境变量

#### 核心配置 (.env)
```bash
# 应用基础配置
APP_NAME=iICrawlerMCP
APP_VERSION=2.0.0
ENVIRONMENT=development  # development/staging/production
DEBUG=true
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=8000
WORKERS=4

# 安全配置
SECRET_KEY=your-very-long-secret-key-here-at-least-32-characters
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
CORS_ORIGINS=http://localhost:3000,https://your-frontend-domain.com
```

#### 数据库配置
```bash
# PostgreSQL配置
DATABASE_URL=postgresql://username:password@localhost:5432/iicrawlermcp
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# 数据库连接重试
DB_RETRY_ATTEMPTS=3
DB_RETRY_DELAY=1.0

# 数据库备份
DB_BACKUP_ENABLED=true
DB_BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
DB_BACKUP_RETENTION_DAYS=30
```

#### 缓存配置
```bash
# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your-redis-password
REDIS_DB=0
REDIS_MAX_CONNECTIONS=50

# 缓存策略
CACHE_TTL=3600  # 1小时
CACHE_MAX_SIZE=1000
CACHE_ENABLED=true

# 会话配置
SESSION_TIMEOUT=86400  # 24小时
SESSION_REDIS_DB=1
```

### AI服务配置

#### OpenAI配置
```bash
# OpenAI API
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=4000
OPENAI_TEMPERATURE=0.1
OPENAI_TIMEOUT=60

# 请求限制
OPENAI_REQUESTS_PER_MINUTE=60
OPENAI_TOKENS_PER_MINUTE=150000
```

#### Anthropic配置
```bash
# Anthropic Claude API
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=4000
ANTHROPIC_TIMEOUT=60

# 备用模型配置
FALLBACK_MODEL_ENABLED=true
FALLBACK_MODEL=gpt-3.5-turbo
```

### 浏览器配置

#### Playwright配置
```bash
# 浏览器设置
BROWSER_TYPE=chromium  # chromium/firefox/webkit
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
BROWSER_SLOW_MO=0

# 浏览器池配置
BROWSER_POOL_SIZE=5
BROWSER_MAX_PAGES_PER_BROWSER=10
BROWSER_IDLE_TIMEOUT=300000  # 5分钟

# 反检测配置
STEALTH_MODE=true
USER_AGENT_ROTATION=true
PROXY_ROTATION=false
```

#### 代理配置
```bash
# 代理设置
PROXY_ENABLED=false
PROXY_LIST=http://proxy1:8080,http://proxy2:8080
PROXY_USERNAME=proxy_user
PROXY_PASSWORD=proxy_pass
PROXY_ROTATION_INTERVAL=300  # 5分钟轮换一次
```

## ⚡ 性能配置

### 并发控制

#### Agent池配置
```python
# config/agent_pool.py
AGENT_POOL_CONFIG = {
    "max_agents": 10,
    "min_agents": 2,
    "scale_up_threshold": 0.8,  # CPU使用率超过80%时扩容
    "scale_down_threshold": 0.3,  # CPU使用率低于30%时缩容
    "scale_interval": 60,  # 扩缩容检查间隔(秒)
    "agent_timeout": 300,  # Agent超时时间(秒)
    "max_retries": 3,
    "retry_delay": 5.0
}
```

#### 任务队列配置
```python
# config/task_queue.py
TASK_QUEUE_CONFIG = {
    "max_queue_size": 1000,
    "priority_levels": 5,
    "batch_size": 20,
    "processing_interval": 1.0,
    "dead_letter_queue": True,
    "max_processing_time": 3600  # 1小时
}
```

### 资源限制

#### 内存管理
```bash
# 内存配置
MAX_MEMORY_PER_AGENT=2048  # MB
MEMORY_WARNING_THRESHOLD=1536  # MB
MEMORY_CLEANUP_INTERVAL=300  # 5分钟
GC_THRESHOLD=100  # 垃圾回收阈值

# 大文件处理
MAX_FILE_SIZE=100  # MB
CHUNK_SIZE=8192  # 8KB
STREAMING_ENABLED=true
```

#### 网络配置
```bash
# 网络设置
REQUEST_TIMEOUT=30
CONNECT_TIMEOUT=10
READ_TIMEOUT=30
MAX_REDIRECTS=5

# 连接池
CONNECTION_POOL_SIZE=100
CONNECTION_POOL_MAXSIZE=200
CONNECTION_POOL_BLOCK=false

# 重试策略
RETRY_ATTEMPTS=3
RETRY_BACKOFF_FACTOR=2.0
RETRY_STATUS_CODES=500,502,503,504
```

## 🔒 安全配置

### 认证授权

#### JWT配置
```bash
# JWT设置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 令牌安全
JWT_ISSUER=iicrawlermcp
JWT_AUDIENCE=iicrawlermcp-users
JWT_REQUIRE_CLAIMS=true
```

#### OAuth配置
```bash
# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8000/auth/google/callback

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GITHUB_REDIRECT_URI=http://localhost:8000/auth/github/callback
```

### 数据安全

#### 加密配置
```bash
# 数据加密
ENCRYPTION_KEY=your-32-character-encryption-key
ENCRYPTION_ALGORITHM=AES-256-GCM
HASH_ALGORITHM=SHA-256

# 敏感数据处理
MASK_SENSITIVE_DATA=true
SENSITIVE_FIELDS=password,credit_card,ssn,phone
LOG_SENSITIVE_DATA=false
```

#### 访问控制
```bash
# IP白名单
IP_WHITELIST_ENABLED=false
IP_WHITELIST=***********/24,10.0.0.0/8

# 速率限制
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600  # 1小时
RATE_LIMIT_STORAGE=redis
```

## 📊 监控配置

### 日志配置

#### 日志级别和格式
```python
# config/logging.py
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        },
        "json": {
            "format": "%(asctime)s %(name)s %(levelname)s %(message)s",
            "class": "pythonjsonlogger.jsonlogger.JsonFormatter"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "detailed"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/app.log",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "formatter": "json"
        }
    },
    "loggers": {
        "iicrawlermcp": {
            "level": "DEBUG",
            "handlers": ["console", "file"],
            "propagate": False
        }
    }
}
```

#### 审计日志
```bash
# 审计配置
AUDIT_LOG_ENABLED=true
AUDIT_LOG_LEVEL=INFO
AUDIT_LOG_FILE=logs/audit.log
AUDIT_LOG_RETENTION_DAYS=90

# 审计事件
AUDIT_LOGIN_EVENTS=true
AUDIT_TASK_EVENTS=true
AUDIT_DATA_ACCESS=true
AUDIT_CONFIG_CHANGES=true
```

### 指标收集

#### Prometheus配置
```yaml
# config/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'iicrawlermcp'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['localhost:9121']
```

#### 自定义指标
```python
# config/metrics.py
CUSTOM_METRICS = {
    "task_duration_histogram": {
        "name": "task_duration_seconds",
        "description": "Task execution duration",
        "buckets": [0.1, 0.5, 1.0, 2.5, 5.0, 10.0, 30.0, 60.0, 300.0]
    },
    "active_agents_gauge": {
        "name": "active_agents_total",
        "description": "Number of active agents"
    },
    "error_rate_counter": {
        "name": "errors_total",
        "description": "Total number of errors",
        "labels": ["error_type", "component"]
    }
}
```

## 🔄 部署配置

### Docker配置

#### Dockerfile优化
```dockerfile
# 多阶段构建配置
ARG PYTHON_VERSION=3.11
ARG NODE_VERSION=18

# 构建阶段
FROM python:${PYTHON_VERSION}-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 运行阶段
FROM python:${PYTHON_VERSION}-slim
WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY . .

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

EXPOSE 8000
CMD ["uvicorn", "src.iicrawlermcp.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

#### Docker Compose配置
```yaml
# docker-compose.override.yml (开发环境)
version: '3.8'

services:
  web:
    build:
      context: .
      target: development
    volumes:
      - .:/app
      - /app/node_modules
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    ports:
      - "8000:8000"
      - "5678:5678"  # 调试端口

  db:
    environment:
      - POSTGRES_DB=iicrawlermcp_dev
    ports:
      - "5432:5432"

  redis:
    ports:
      - "6379:6379"
```

### Kubernetes配置

#### ConfigMap
```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: iicrawlermcp-config
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  BROWSER_HEADLESS: "true"
  CACHE_ENABLED: "true"
  RATE_LIMIT_ENABLED: "true"
```

#### 资源限制
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: iicrawlermcp-web
spec:
  template:
    spec:
      containers:
      - name: web
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        env:
        - name: MAX_MEMORY_PER_AGENT
          value: "1024"
        - name: AGENT_POOL_SIZE
          value: "3"
```

## 🔧 配置验证

### 配置检查脚本
```python
# scripts/check_config.py
import os
import sys
from typing import Dict, Any

def validate_config() -> Dict[str, Any]:
    """验证配置的完整性和正确性"""
    errors = []
    warnings = []
    
    # 检查必需的环境变量
    required_vars = [
        'DATABASE_URL',
        'REDIS_URL',
        'SECRET_KEY',
        'OPENAI_API_KEY'
    ]
    
    for var in required_vars:
        if not os.getenv(var):
            errors.append(f"Missing required environment variable: {var}")
    
    # 检查数据库连接
    try:
        from src.iicrawlermcp.database import test_connection
        if not test_connection():
            errors.append("Database connection failed")
    except Exception as e:
        errors.append(f"Database connection error: {e}")
    
    # 检查Redis连接
    try:
        from src.iicrawlermcp.cache import test_redis_connection
        if not test_redis_connection():
            errors.append("Redis connection failed")
    except Exception as e:
        errors.append(f"Redis connection error: {e}")
    
    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "warnings": warnings
    }

if __name__ == "__main__":
    result = validate_config()
    if result["valid"]:
        print("✅ Configuration is valid")
        sys.exit(0)
    else:
        print("❌ Configuration errors found:")
        for error in result["errors"]:
            print(f"  - {error}")
        sys.exit(1)
```

### 配置模板生成
```bash
# scripts/generate_config.sh
#!/bin/bash

echo "Generating configuration files..."

# 生成.env文件
cat > .env << EOF
# 基础配置
APP_NAME=iICrawlerMCP
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# 数据库配置
DATABASE_URL=postgresql://iicrawler:password@localhost:5432/iicrawlermcp
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=$(openssl rand -hex 32)
JWT_SECRET_KEY=$(openssl rand -hex 32)

# AI服务配置
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# 浏览器配置
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000
EOF

echo "✅ Configuration files generated successfully"
echo "📝 Please edit .env file with your actual API keys and database credentials"
```

---

*最后更新: 2025-01-29*
