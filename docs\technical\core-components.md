# 核心组件详解

## 📋 概述

本文档详细介绍iICrawlerMCP系统的核心组件，包括其设计理念、实现细节和使用方法。

## 🏗️ 系统架构层次

### L0: 用户接口层

#### Web界面 (React + TypeScript)
```typescript
// 主要组件结构
interface WebUIComponents {
  TaskCreator: React.FC;      // 任务创建组件
  TaskMonitor: React.FC;      // 任务监控组件
  ResultViewer: React.FC;     // 结果查看组件
  SettingsPanel: React.FC;    // 设置面板
}

// 实时通信
const useWebSocket = () => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  
  useEffect(() => {
    const ws = new WebSocket('ws://localhost:8000/ws');
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      handleTaskUpdate(data);
    };
    setSocket(ws);
  }, []);
};
```

#### REST API (FastAPI)
```python
# API路由结构
@app.post("/api/tasks")
async def create_task(task: TaskRequest) -> TaskResponse:
    """创建新的爬取任务"""
    
@app.get("/api/tasks/{task_id}/status")
async def get_task_status(task_id: str) -> TaskStatus:
    """获取任务状态"""
    
@app.get("/api/tasks/{task_id}/results")
async def get_task_results(task_id: str) -> TaskResults:
    """获取任务结果"""
```

### L1: 顶级监督层

#### TopSupervisor (全局协调器)
```python
class TopSupervisor:
    """顶级监督者，负责全局任务协调"""
    
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-4")
        self.workflow_orchestrator = WorkflowOrchestrator()
        self.global_state_manager = GlobalStateManager()
        self.domain_supervisors = self._init_domain_supervisors()
    
    async def process_request(self, request: UserRequest) -> TaskResult:
        """处理用户请求的主要流程"""
        # 1. 分析请求
        analysis = await self.analyze_request(request)
        
        # 2. 规划工作流
        workflow = await self.plan_workflow(analysis)
        
        # 3. 分配任务
        await self.delegate_tasks(workflow)
        
        # 4. 监控执行
        result = await self.monitor_execution()
        
        return result
    
    async def analyze_request(self, request: UserRequest) -> RequestAnalysis:
        """使用LLM分析用户请求"""
        prompt = f"""
        分析以下爬虫任务请求：
        目标网站: {request.target_url}
        数据字段: {request.data_fields}
        
        请提供：
        1. 任务复杂度评估 (1-10)
        2. 预估执行时间
        3. 所需资源类型
        4. 潜在风险点
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        return RequestAnalysis.from_llm_response(response.content)
```

#### WorkflowOrchestrator (工作流编排器)
```python
class WorkflowOrchestrator:
    """工作流编排器，负责任务流程设计"""
    
    def __init__(self):
        self.workflow_templates = self._load_templates()
        self.dependency_resolver = DependencyResolver()
    
    async def create_workflow(self, analysis: RequestAnalysis) -> Workflow:
        """根据分析结果创建工作流"""
        # 选择合适的模板
        template = self._select_template(analysis)
        
        # 自定义工作流
        workflow = self._customize_workflow(template, analysis)
        
        # 解析依赖关系
        workflow = self.dependency_resolver.resolve(workflow)
        
        return workflow
    
    def _select_template(self, analysis: RequestAnalysis) -> WorkflowTemplate:
        """选择最适合的工作流模板"""
        if analysis.complexity_score <= 3:
            return self.workflow_templates["simple_crawl"]
        elif analysis.complexity_score <= 7:
            return self.workflow_templates["standard_crawl"]
        else:
            return self.workflow_templates["complex_crawl"]
```

### L2: 领域监督层

#### RecordSupervisor (录制监督者)
```python
class RecordSupervisor:
    """录制领域监督者，管理用户操作录制"""
    
    def __init__(self):
        self.browser_manager = BrowserManager()
        self.action_recorder = ActionRecorder()
        self.sequence_optimizer = SequenceOptimizer()
    
    async def start_recording_session(self, config: RecordingConfig) -> RecordingSession:
        """启动录制会话"""
        # 准备浏览器环境
        browser = await self.browser_manager.launch_browser(config)
        
        # 创建录制会话
        session = RecordingSession(
            browser=browser,
            config=config,
            recorder=self.action_recorder
        )
        
        # 开始录制
        await session.start()
        
        return session
    
    async def process_recorded_actions(self, actions: List[UserAction]) -> ActionSequence:
        """处理录制的用户操作"""
        # 清理和验证操作序列
        cleaned_actions = self._clean_actions(actions)
        
        # 优化操作序列
        optimized_sequence = await self.sequence_optimizer.optimize(cleaned_actions)
        
        # 生成稳定的选择器
        stable_sequence = await self._generate_stable_selectors(optimized_sequence)
        
        return stable_sequence
```

#### CodeSupervisor (代码生成监督者)
```python
class CodeSupervisor:
    """代码生成监督者，管理爬虫代码生成"""
    
    def __init__(self):
        self.code_generator = CodeGenerator()
        self.code_validator = CodeValidator()
        self.template_manager = TemplateManager()
    
    async def generate_crawler_code(self, sequence: ActionSequence) -> CrawlerCode:
        """根据操作序列生成爬虫代码"""
        # 分析操作模式
        patterns = await self._analyze_patterns(sequence)
        
        # 选择代码模板
        template = self.template_manager.select_template(patterns)
        
        # 生成代码
        code = await self.code_generator.generate(template, sequence)
        
        # 验证代码质量
        validation_result = await self.code_validator.validate(code)
        
        if not validation_result.is_valid:
            # 修复代码问题
            code = await self._fix_code_issues(code, validation_result.issues)
        
        return code
    
    async def _analyze_patterns(self, sequence: ActionSequence) -> List[Pattern]:
        """分析操作序列中的模式"""
        patterns = []
        
        # 检测列表遍历模式
        if self._has_list_iteration(sequence):
            patterns.append(Pattern.LIST_ITERATION)
        
        # 检测分页模式
        if self._has_pagination(sequence):
            patterns.append(Pattern.PAGINATION)
        
        # 检测表单填写模式
        if self._has_form_filling(sequence):
            patterns.append(Pattern.FORM_FILLING)
        
        return patterns
```

### L3: 专门Agent层

#### RecordAgent (录制Agent)
```python
class RecordAgent:
    """录制Agent，负责具体的操作录制"""
    
    def __init__(self):
        self.playwright_manager = PlaywrightManager()
        self.element_analyzer = ElementAnalyzer()
        self.action_tracker = ActionTracker()
    
    async def record_user_actions(self, page: Page) -> List[UserAction]:
        """录制用户操作"""
        actions = []
        
        # 设置事件监听器
        await self._setup_event_listeners(page, actions)
        
        # 等待用户操作
        await self._wait_for_user_completion(page)
        
        # 后处理操作序列
        processed_actions = await self._post_process_actions(actions)
        
        return processed_actions
    
    async def _setup_event_listeners(self, page: Page, actions: List[UserAction]):
        """设置页面事件监听器"""
        # 点击事件
        await page.add_listener("click", lambda event: self._handle_click(event, actions))
        
        # 输入事件
        await page.add_listener("input", lambda event: self._handle_input(event, actions))
        
        # 导航事件
        await page.add_listener("framenavigated", lambda event: self._handle_navigation(event, actions))
```

#### CodeGenAgent (代码生成Agent)
```python
class CodeGenAgent:
    """代码生成Agent，负责具体的代码生成"""
    
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-4")
        self.code_templates = CodeTemplateLibrary()
        self.syntax_checker = SyntaxChecker()
    
    async def generate_playwright_code(self, sequence: ActionSequence) -> str:
        """生成Playwright爬虫代码"""
        # 构建代码生成提示
        prompt = self._build_code_generation_prompt(sequence)
        
        # 使用LLM生成代码
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        
        # 提取和清理代码
        code = self._extract_code_from_response(response.content)
        
        # 语法检查和修复
        validated_code = await self.syntax_checker.check_and_fix(code)
        
        return validated_code
    
    def _build_code_generation_prompt(self, sequence: ActionSequence) -> str:
        """构建代码生成提示"""
        return f"""
        基于以下用户操作序列生成Playwright爬虫代码：
        
        操作序列：
        {self._format_sequence_for_prompt(sequence)}
        
        要求：
        1. 使用async/await语法
        2. 包含错误处理
        3. 添加适当的等待和重试机制
        4. 使用稳定的元素选择器
        5. 包含数据验证逻辑
        
        请生成完整的Python代码。
        """
```

### L4: 工具层

#### BrowserTools (浏览器工具)
```python
class BrowserTools:
    """浏览器操作工具集"""
    
    def __init__(self):
        self.playwright = None
        self.browser_pool = BrowserPool()
        self.stealth_config = StealthConfig()
    
    async def launch_browser(self, config: BrowserConfig) -> Browser:
        """启动浏览器实例"""
        if not self.playwright:
            self.playwright = await async_playwright().start()
        
        browser = await self.playwright.chromium.launch(
            headless=config.headless,
            args=self._get_browser_args(config),
            **self.stealth_config.get_launch_options()
        )
        
        return browser
    
    async def create_stealth_page(self, browser: Browser) -> Page:
        """创建隐身页面"""
        context = await browser.new_context(
            **self.stealth_config.get_context_options()
        )
        
        page = await context.new_page()
        
        # 注入反检测脚本
        await self._inject_stealth_scripts(page)
        
        return page
    
    async def smart_wait(self, page: Page, selector: str, timeout: int = 30000):
        """智能等待元素"""
        try:
            # 等待元素出现
            await page.wait_for_selector(selector, timeout=timeout)
            
            # 等待网络空闲
            await page.wait_for_load_state("networkidle")
            
            # 等待元素稳定
            await self._wait_for_element_stable(page, selector)
            
        except TimeoutError:
            # 尝试滚动加载
            await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            await page.wait_for_timeout(2000)
            
            # 再次尝试
            await page.wait_for_selector(selector, timeout=5000)
```

#### DataTools (数据处理工具)
```python
class DataTools:
    """数据处理工具集"""
    
    def __init__(self):
        self.cleaners = DataCleaners()
        self.validators = DataValidators()
        self.transformers = DataTransformers()
    
    def clean_extracted_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """清理提取的数据"""
        cleaned_data = {}
        
        for field, value in raw_data.items():
            if isinstance(value, str):
                # 文本清理
                cleaned_value = self.cleaners.clean_text(value)
            elif isinstance(value, (int, float)):
                # 数值清理
                cleaned_value = self.cleaners.clean_number(value)
            elif isinstance(value, list):
                # 列表清理
                cleaned_value = [self.clean_extracted_data(item) if isinstance(item, dict) else item for item in value]
            else:
                cleaned_value = value
            
            cleaned_data[field] = cleaned_value
        
        return cleaned_data
    
    def validate_data_quality(self, data: List[Dict[str, Any]]) -> ValidationReport:
        """验证数据质量"""
        report = ValidationReport()
        
        for item in data:
            # 检查必填字段
            missing_fields = self.validators.check_required_fields(item)
            if missing_fields:
                report.add_error(f"Missing fields: {missing_fields}")
            
            # 检查数据格式
            format_errors = self.validators.check_data_formats(item)
            report.add_errors(format_errors)
            
            # 检查数据一致性
            consistency_errors = self.validators.check_consistency(item)
            report.add_errors(consistency_errors)
        
        return report
```

## 🔄 组件交互流程

### 典型任务执行流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as Web界面
    participant TS as TopSupervisor
    participant RS as RecordSupervisor
    participant CS as CodeSupervisor
    participant ES as ExecSupervisor
    
    User->>UI: 创建任务
    UI->>TS: 提交任务请求
    TS->>TS: 分析请求
    TS->>RS: 分配录制任务
    RS->>User: 启动录制会话
    User->>RS: 完成操作演示
    RS->>CS: 提交操作序列
    CS->>CS: 生成爬虫代码
    CS->>ES: 提交执行任务
    ES->>ES: 执行爬虫代码
    ES->>TS: 返回执行结果
    TS->>UI: 返回最终结果
    UI->>User: 显示结果
```

## 🔧 扩展和自定义

### 添加新的Agent类型
```python
class CustomAgent(BaseAgent):
    """自定义Agent示例"""
    
    def __init__(self, config: AgentConfig):
        super().__init__(config)
        self.custom_tools = CustomToolset()
    
    async def execute_task(self, task: Task) -> TaskResult:
        """执行自定义任务"""
        # 实现自定义逻辑
        pass
    
    async def handle_error(self, error: Exception) -> ErrorHandlingResult:
        """处理错误"""
        # 实现错误处理逻辑
        pass

# 注册自定义Agent
agent_registry.register("custom_agent", CustomAgent)
```

### 添加新的工具
```python
class CustomTool(BaseTool):
    """自定义工具示例"""
    
    name = "custom_tool"
    description = "自定义工具描述"
    
    async def execute(self, **kwargs) -> ToolResult:
        """执行工具逻辑"""
        # 实现工具功能
        pass

# 注册自定义工具
tool_registry.register(CustomTool)
```

## 📊 性能监控

### 组件性能指标
```python
class ComponentMetrics:
    """组件性能指标"""
    
    def __init__(self):
        self.execution_times = {}
        self.error_counts = {}
        self.success_rates = {}
    
    def record_execution_time(self, component: str, duration: float):
        """记录执行时间"""
        if component not in self.execution_times:
            self.execution_times[component] = []
        self.execution_times[component].append(duration)
    
    def get_average_execution_time(self, component: str) -> float:
        """获取平均执行时间"""
        times = self.execution_times.get(component, [])
        return sum(times) / len(times) if times else 0.0
```

---

*最后更新: 2025-01-29*
