#!/usr/bin/env python3
"""
iICrawlerMCP MCP服务器启动脚本

这个脚本启动iICrawlerMCP的MCP服务器，提供标准MCP协议接口。

使用方法:
    python scripts/start_mcp_server.py
    
或者安装后使用:
    iicrawlermcp-mcp
"""

import sys
import os
import asyncio
import argparse
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.iicrawlermcp.mcp.server import start_mcp_server
except ImportError as e:
    print(f"❌ 导入失败: {e}", file=sys.stderr)
    print("请确保已安装所有依赖: pip install -r requirements.txt", file=sys.stderr)
    sys.exit(1)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="启动iICrawlerMCP的MCP服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python scripts/start_mcp_server.py
    python scripts/start_mcp_server.py --name my-crawler
    
环境变量:
    OPENAI_API_KEY     - OpenAI API密钥 (必需)
    OPENAI_API_BASE    - OpenAI API基础URL (可选)
    OPENAI_MODEL       - 使用的模型 (默认: gpt-3.5-turbo)
    HEADLESS          - 无头模式 (默认: true)
    VERBOSE           - 详细日志 (默认: true)
        """
    )
    
    parser.add_argument(
        "--name",
        default="iicrawlermcp",
        help="MCP服务器名称 (默认: iicrawlermcp)"
    )
    
    parser.add_argument(
        "--check-config",
        action="store_true",
        help="检查配置后退出"
    )
    
    return parser.parse_args()


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...", file=sys.stderr)
    
    # 检查必需的环境变量
    required_env = ["OPENAI_API_KEY"]
    missing_env = []
    
    for env_var in required_env:
        if not os.getenv(env_var):
            missing_env.append(env_var)
    
    if missing_env:
        print(f"❌ 缺少必需的环境变量: {', '.join(missing_env)}", file=sys.stderr)
        print("请设置以下环境变量:", file=sys.stderr)
        for env_var in missing_env:
            print(f"  export {env_var}=your_value", file=sys.stderr)
        return False
    
    # 检查可选环境变量
    optional_env = {
        "OPENAI_API_BASE": "未设置 (使用默认)",
        "OPENAI_MODEL": os.getenv("OPENAI_MODEL", "gpt-3.5-turbo"),
        "HEADLESS": os.getenv("HEADLESS", "true"),
        "VERBOSE": os.getenv("VERBOSE", "true")
    }
    
    print("✅ 环境变量检查通过:", file=sys.stderr)
    for env_var, value in optional_env.items():
        actual_value = os.getenv(env_var, value)
        print(f"  {env_var}: {actual_value}", file=sys.stderr)
    
    return True


def main():
    """主函数"""
    args = parse_arguments()
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 如果只是检查配置，则退出
    if args.check_config:
        print("✅ 配置检查完成", file=sys.stderr)
        return
    
    # 显示启动信息
    print("🚀 启动iICrawlerMCP服务器...", file=sys.stderr)
    print(f"📡 服务器名称: {args.name}", file=sys.stderr)
    print("📡 MCP协议: stdio", file=sys.stderr)
    print("🤖 主Agent: CrawlerAgent", file=sys.stderr)
    print("🔧 可用工具:", file=sys.stderr)
    print("   - intelligent_web_task: 智能网页任务统一入口", file=sys.stderr)
    print("   - browser_status: 浏览器状态查询", file=sys.stderr)
    print("   - take_screenshot: 快速截图", file=sys.stderr)
    print("   - cleanup_browser: 资源清理", file=sys.stderr)
    print("-" * 50, file=sys.stderr)
    print("💡 提示: 使用Ctrl+C停止服务器", file=sys.stderr)
    print("-" * 50, file=sys.stderr)
    
    try:
        # 启动MCP服务器
        asyncio.run(start_mcp_server(args.name))
    except KeyboardInterrupt:
        print("\n👋 服务器已停止", file=sys.stderr)
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
