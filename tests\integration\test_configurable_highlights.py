#!/usr/bin/env python3
"""
测试可配置的高亮框功能
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_configurable_highlights():
    """
    测试可配置的高亮框功能
    """
    
    print("🎨 可配置高亮框功能测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        # 显示当前高亮框配置
        highlight_config = config.get_highlight_config()
        print("\n🎨 当前高亮框配置：")
        print(f"   - 启用高亮: {highlight_config['doHighlightElements']}")
        print(f"   - 边框宽度: {highlight_config['borderWidth']}px")
        print(f"   - 透明度: {highlight_config['opacity']}")
        print(f"   - Z-Index: {highlight_config['zIndex']}")
        print(f"   - 标签字体大小: {highlight_config['labelFontSizeMin']}-{highlight_config['labelFontSizeMax']}px")
        print(f"   - 标签内边距: {highlight_config['labelPadding']}")
        print(f"   - 标签圆角: {highlight_config['labelBorderRadius']}")
        print(f"   - 颜色数量: {len(highlight_config['colors'])} 种")
        print(f"   - 颜色列表: {', '.join(highlight_config['colors'][:5])}...")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 测试任务：使用配置化的高亮框
        task_description = """
        请执行以下任务来测试配置化的高亮框功能：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 分析页面上的表单元素（使用配置化的高亮框样式）
        3. 截取页面截图，展示配置化的高亮框效果
        4. 清理高亮框
        5. 再次截图验证清理效果
        
        高亮框应该使用.env文件中配置的样式参数。
        """
        
        print("\n🚀 开始可配置高亮框测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Configurable highlights test error: {e}")
        return False


def test_different_configurations():
    """
    测试不同的配置参数
    """
    
    print("🔧 不同配置参数测试")
    print("=" * 50)
    
    try:
        # 临时修改配置进行测试
        original_colors = config.DOM_HIGHLIGHT_COLORS
        original_border_width = config.DOM_HIGHLIGHT_BORDER_WIDTH
        original_opacity = config.DOM_HIGHLIGHT_OPACITY
        
        # 测试配置1：粗边框，高透明度
        print("\n🎨 测试配置1：粗边框，高透明度")
        config.DOM_HIGHLIGHT_BORDER_WIDTH = 4
        config.DOM_HIGHLIGHT_OPACITY = 0.3
        config.DOM_HIGHLIGHT_COLORS = "#FF0000,#00FF00,#0000FF"
        
        highlight_config = config.get_highlight_config()
        print(f"   - 边框宽度: {highlight_config['borderWidth']}px")
        print(f"   - 透明度: {highlight_config['opacity']}")
        print(f"   - 颜色: {highlight_config['colors']}")
        
        # 恢复原始配置
        config.DOM_HIGHLIGHT_COLORS = original_colors
        config.DOM_HIGHLIGHT_BORDER_WIDTH = original_border_width
        config.DOM_HIGHLIGHT_OPACITY = original_opacity
        
        print("✅ 配置测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 配置测试失败: {e}")
        logger.error(f"Configuration test error: {e}")
        return False


def main():
    """主函数"""
    
    print("🎨 可配置高亮框功能测试")
    print("=" * 60)
    print("测试.env文件中的高亮框配置参数")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 可配置高亮框功能测试")
    print("2. 不同配置参数测试")
    print("3. 显示当前配置")
    
    choice = input("\n请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        print("\n🎨 执行可配置高亮框功能测试...")
        result = test_configurable_highlights()
    elif choice == "2":
        print("\n🔧 执行不同配置参数测试...")
        result = test_different_configurations()
    elif choice == "3":
        print("\n📋 显示当前配置...")
        try:
            config.validate()
            highlight_config = config.get_highlight_config()
            print("\n🎨 当前高亮框配置：")
            for key, value in highlight_config.items():
                if key == 'colors':
                    print(f"   - {key}: {len(value)} 种颜色 {value[:3]}...")
                else:
                    print(f"   - {key}: {value}")
            result = True
        except Exception as e:
            print(f"❌ 配置显示失败: {e}")
            result = False
    else:
        print("👋 无效选择，执行可配置高亮框功能测试...")
        result = test_configurable_highlights()
    
    if result:
        print("\n🎉 可配置高亮框测试成功！")
        print("\n💡 实现的功能：")
        print("• ✅ .env文件配置 - 所有高亮框参数可配置")
        print("• ✅ 颜色自定义 - 支持自定义颜色列表")
        print("• ✅ 样式配置 - 边框宽度、透明度、字体大小等")
        print("• ✅ 配置验证 - 自动验证配置参数有效性")
        print("• ✅ 默认值 - 提供合理的默认配置")
        print("• ✅ 示例文件 - .env.example提供配置示例")
        
        print("\n🔧 配置参数说明：")
        print("• DOM_HIGHLIGHT_ENABLED - 启用/禁用高亮框")
        print("• DOM_HIGHLIGHT_BORDER_WIDTH - 边框宽度(px)")
        print("• DOM_HIGHLIGHT_OPACITY - 背景透明度(0.0-1.0)")
        print("• DOM_HIGHLIGHT_COLORS - 颜色列表(逗号分隔)")
        print("• DOM_HIGHLIGHT_LABEL_* - 标签样式配置")
        
        return 0
    else:
        print("\n❌ 可配置高亮框测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
