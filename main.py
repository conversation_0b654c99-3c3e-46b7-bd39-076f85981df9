#!/usr/bin/env python3
"""
Main entry point for iICrawlerMCP.

This script demonstrates the basic usage of the iICrawlerMCP package
for web crawling with LangChain agents and Playwright.
"""

import logging
import sys
from src.iicrawlermcp.agents import build_agent
from src.iicrawlermcp.core import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function to demonstrate web crawling functionality."""
    try:
        # Validate configuration
        config.validate()
        logger.info("Configuration validated successfully")
        
        # Build the agent
        logger.info("Building crawler agent...")
        agent = build_agent()
        
        # Example task: Navigate to Google and take a screenshot
        task = "打开google.com，搜索迪士尼乐园，点击第一个非广告的结果，然后给我一个迪士尼乐园的页面截图"
        logger.info(f"Executing task: {task}")
        
        # Execute the task
        result = agent.invoke(task)
        
        # Print the result
        print("\n" + "="*50)
        print("TASK COMPLETED")
        print("="*50)
        print(f"Output: {result['output']}")
        print("="*50)
        
        # Clean up resources
        agent.cleanup()
        logger.info("Application completed successfully")
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Application failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
