#!/usr/bin/env python3
"""
端到端多智能体协作测试

这个测试文件验证了iICrawlerMCP项目中多智能体协作、消息规范化、
agent通讯机制等核心改进的完整工作流程。

测试场景：
1. 多智能体协作完成复杂任务
2. 标准化消息格式验证
3. Agent委托机制测试
4. 结构化输出验证
5. 错误处理和重试机制
"""

import pytest
import logging
import json
import time
from typing import Dict, Any
from datetime import datetime

from iicrawlermcp.agents import build_agent, build_browser_agent, build_element_agent
from iicrawlermcp.core.config import config
from iicrawlermcp.core.schemas import (
    AgentRequest, AgentResponse, SimpleExecutionResult,
    TaskType, ExecutionStatus, TaskStep, TaskPlan
)
from iicrawlermcp.agents.structured_agent_base import StructuredAgentBase

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestMultiAgentE2E:
    """端到端多智能体协作测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """测试前后的设置和清理"""
        logger.info("🚀 开始端到端测试")
        yield
        logger.info("🧹 清理测试资源")
        try:
            from iicrawlermcp.core.browser import cleanup_global_browser
            cleanup_global_browser()
        except Exception as e:
            logger.warning(f"清理警告: {e}")
    
    def test_01_configuration_validation(self):
        """测试1: 配置验证"""
        logger.info("📋 测试配置验证")
        
        try:
            config.validate()
            logger.info("✅ 配置验证通过")
            assert True
        except Exception as e:
            pytest.skip(f"配置验证失败，跳过测试: {e}")
    
    def test_02_individual_agent_creation(self):
        """测试2: 各个智能体的独立创建和基本功能"""
        logger.info("🤖 测试智能体创建")
        
        # 测试CrawlerAgent创建
        crawler_agent = build_agent()
        assert crawler_agent is not None
        logger.info("✅ CrawlerAgent创建成功")
        
        # 测试BrowserAgent创建
        browser_agent = build_browser_agent()
        assert browser_agent is not None
        logger.info("✅ BrowserAgent创建成功")
        
        # 测试ElementAgent创建
        element_agent = build_element_agent()
        assert element_agent is not None
        logger.info("✅ ElementAgent创建成功")
        
        # 清理
        crawler_agent.cleanup()
        browser_agent.cleanup()
        element_agent.cleanup()
    
    def test_03_structured_message_format(self):
        """测试3: 结构化消息格式验证"""
        logger.info("📨 测试结构化消息格式")
        
        # 创建标准化请求
        request = AgentRequest(
            agent_type="browser",
            input_data={"url": "https://httpbin.org/html"},
            expected_output_schema="SimpleExecutionResult",
            metadata={"test": "e2e_validation"}
        )
        
        # 验证请求格式
        assert request.request_id is not None
        assert request.timestamp is not None
        assert request.agent_type == "browser"
        assert "url" in request.input_data
        logger.info("✅ AgentRequest格式验证通过")
        
        # 创建标准化响应
        response = AgentResponse(
            request_id=request.request_id,
            status="success",
            data={"result": "test_data"},
            processing_time_seconds=1.5
        )
        
        # 验证响应格式
        assert response.request_id == request.request_id
        assert response.status == "success"
        assert response.data is not None
        logger.info("✅ AgentResponse格式验证通过")
    
    def test_04_browser_agent_delegation(self):
        """测试4: 浏览器智能体委托机制"""
        logger.info("🌐 测试浏览器智能体委托")
        
        crawler_agent = build_agent()
        
        try:
            # 委托浏览器任务
            task = "Navigate to https://httpbin.org/html and take a screenshot"
            result = crawler_agent.delegate_browser_task(task)
            
            # 验证结果
            assert isinstance(result, dict)
            assert "output" in result
            logger.info(f"✅ 浏览器委托成功: {result['output'][:100]}...")
            
        finally:
            crawler_agent.cleanup()
    
    def test_05_element_agent_delegation(self):
        """测试5: 元素智能体委托机制"""
        logger.info("🔍 测试元素智能体委托")
        
        crawler_agent = build_agent()
        
        try:
            # 先导航到页面
            crawler_agent.delegate_browser_task("Navigate to https://httpbin.org/forms/post")
            
            # 委托元素分析任务
            task = "Find all form elements and describe their types"
            result = crawler_agent.delegate_element_task(task)
            
            # 验证结果
            assert isinstance(result, dict)
            assert "output" in result
            logger.info(f"✅ 元素委托成功: {result['output'][:100]}...")
            
        finally:
            crawler_agent.cleanup()
    
    def test_06_delegation_tools_integration(self):
        """测试6: 委托工具集成测试"""
        logger.info("🔧 测试委托工具集成")
        
        crawler_agent = build_agent()
        
        try:
            # 使用智能元素查找工具
            task = """
            Navigate to https://httpbin.org/forms/post and then use the smart element finder 
            to locate the submit button and describe its properties
            """
            result = crawler_agent.invoke(task)
            
            # 验证结果
            assert isinstance(result, dict)
            assert "output" in result
            logger.info(f"✅ 委托工具集成成功: {result['output'][:100]}...")
            
        finally:
            crawler_agent.cleanup()
    
    def test_07_complex_multi_agent_workflow(self):
        """测试7: 复杂多智能体工作流"""
        logger.info("🎯 测试复杂多智能体工作流")
        
        crawler_agent = build_agent()
        
        try:
            # 复杂的多步骤任务，涉及多个智能体协作
            complex_task = """
            Please complete this complex workflow:
            1. Navigate to https://httpbin.org/forms/post
            2. Analyze the page structure and identify all form elements
            3. Take a screenshot of the form
            4. Fill in the customer name field with 'Test User'
            5. Fill in the email field with '<EMAIL>'
            6. Take a final screenshot showing the filled form
            """
            
            start_time = time.time()
            result = crawler_agent.invoke(complex_task)
            execution_time = time.time() - start_time
            
            # 验证结果
            assert isinstance(result, dict)
            assert "output" in result
            assert execution_time < 120  # 应该在2分钟内完成
            
            logger.info(f"✅ 复杂工作流完成，耗时: {execution_time:.2f}秒")
            logger.info(f"结果: {result['output'][:150]}...")
            
        finally:
            crawler_agent.cleanup()
    
    def test_08_error_handling_and_recovery(self):
        """测试8: 错误处理和恢复机制"""
        logger.info("⚠️ 测试错误处理和恢复")
        
        crawler_agent = build_agent()
        
        try:
            # 故意使用无效URL测试错误处理
            invalid_task = "Navigate to https://invalid-url-that-does-not-exist.com"
            result = crawler_agent.invoke(invalid_task)
            
            # 验证错误被正确处理
            assert isinstance(result, dict)
            assert "output" in result
            # 应该包含错误信息但不会崩溃
            logger.info(f"✅ 错误处理正常: {result['output'][:100]}...")
            
        finally:
            crawler_agent.cleanup()
    
    def test_09_structured_output_validation(self):
        """测试9: 结构化输出验证"""
        logger.info("📊 测试结构化输出验证")
        
        # 创建结构化智能体实例
        browser_agent = build_browser_agent()
        
        try:
            # 测试结构化输出
            if hasattr(browser_agent, 'invoke_structured'):
                input_data = {"task": "Navigate to https://httpbin.org/html"}
                result = browser_agent.invoke_structured(
                    input_data, 
                    SimpleExecutionResult
                )
                
                # 验证结构化输出
                assert isinstance(result, SimpleExecutionResult)
                assert hasattr(result, 'output')
                assert hasattr(result, 'success')
                logger.info("✅ 结构化输出验证通过")
            else:
                logger.info("⚠️ 当前智能体不支持结构化输出，跳过测试")
                
        finally:
            browser_agent.cleanup()
    
    def test_10_performance_and_resource_management(self):
        """测试10: 性能和资源管理"""
        logger.info("⚡ 测试性能和资源管理")
        
        start_time = time.time()
        
        # 创建多个智能体实例测试资源管理
        agents = []
        try:
            for i in range(3):
                agent = build_agent()
                agents.append(agent)
                
                # 执行简单任务
                result = agent.invoke("Navigate to https://httpbin.org/html")
                assert isinstance(result, dict)
                
            creation_time = time.time() - start_time
            logger.info(f"✅ 创建3个智能体耗时: {creation_time:.2f}秒")
            
            # 验证性能合理
            assert creation_time < 30  # 应该在30秒内完成
            
        finally:
            # 清理所有智能体
            for agent in agents:
                agent.cleanup()
            
            cleanup_time = time.time() - start_time - creation_time
            logger.info(f"✅ 清理完成，总耗时: {cleanup_time:.2f}秒")


def run_comprehensive_e2e_test():
    """
    运行完整的端到端测试
    
    这个函数可以独立运行，不依赖pytest框架
    """
    print("🚀 开始iICrawlerMCP端到端综合测试")
    print("=" * 60)
    
    test_instance = TestMultiAgentE2E()
    test_methods = [
        ("配置验证", test_instance.test_01_configuration_validation),
        ("智能体创建", test_instance.test_02_individual_agent_creation),
        ("消息格式验证", test_instance.test_03_structured_message_format),
        ("浏览器委托", test_instance.test_04_browser_agent_delegation),
        ("元素委托", test_instance.test_05_element_agent_delegation),
        ("委托工具集成", test_instance.test_06_delegation_tools_integration),
        ("复杂工作流", test_instance.test_07_complex_multi_agent_workflow),
        ("错误处理", test_instance.test_08_error_handling_and_recovery),
        ("结构化输出", test_instance.test_09_structured_output_validation),
        ("性能管理", test_instance.test_10_performance_and_resource_management),
    ]
    
    results = {}
    total_start_time = time.time()
    
    for test_name, test_method in test_methods:
        print(f"\n🧪 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            test_start = time.time()
            test_method()
            test_time = time.time() - test_start
            results[test_name] = {"status": "PASS", "time": test_time}
            print(f"✅ {test_name} - 通过 ({test_time:.2f}秒)")
        except Exception as e:
            test_time = time.time() - test_start
            results[test_name] = {"status": "FAIL", "time": test_time, "error": str(e)}
            print(f"❌ {test_name} - 失败: {e}")
    
    total_time = time.time() - total_start_time
    
    # 打印测试总结
    print("\n" + "=" * 60)
    print("📊 测试总结报告")
    print("=" * 60)
    
    passed = sum(1 for r in results.values() if r["status"] == "PASS")
    total = len(results)
    
    for test_name, result in results.items():
        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"{status_icon} {test_name}: {result['status']} ({result['time']:.2f}秒)")
        if result["status"] == "FAIL":
            print(f"   错误: {result.get('error', 'Unknown error')}")
    
    print(f"\n📈 总体结果: {passed}/{total} 测试通过")
    print(f"⏱️ 总执行时间: {total_time:.2f}秒")
    
    if passed == total:
        print("🎉 所有测试通过！多智能体协作系统工作正常。")
        return True
    else:
        print("⚠️ 部分测试失败，请检查系统配置和网络连接。")
        return False


if __name__ == "__main__":
    import sys
    success = run_comprehensive_e2e_test()
    sys.exit(0 if success else 1)
