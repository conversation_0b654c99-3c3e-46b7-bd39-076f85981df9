#!/usr/bin/env python3
"""
简单的可视化浏览器测试
"""

import sys
import os
import pytest
# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.core.config import config
from iicrawlermcp.core.browser import get_global_browser, close_global_browser
import time

def test_browser():
    print("🎭 可视化浏览器测试")
    print("=" * 30)
    
    # 检查配置
    print(f"⚙️ HEADLESS配置: {config.HEADLESS}")
    
    if config.HEADLESS:
        print("⚠️ 当前是headless模式，看不到浏览器窗口")
        print("💡 要看到浏览器，请在.env文件中设置 HEADLESS=false")
        return
    
    try:
        print("📱 创建浏览器...")
        browser = get_global_browser()
        print(f"✅ 浏览器创建成功，headless={browser.headless}")
        
        print("🌐 导航到Google...")
        result = browser.navigate("https://www.google.com")
        print(f"✅ 导航成功: {result}")
        
        print("⏱️ 等待5秒观察浏览器...")
        time.sleep(5)
        
        print("🔒 关闭浏览器...")
        close_global_browser()
        print("✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
