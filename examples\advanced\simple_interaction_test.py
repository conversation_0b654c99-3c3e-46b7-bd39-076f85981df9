#!/usr/bin/env python3
"""
简单交互动作测试

这个示例专注于测试基本的交互功能：
1. 文本输入
2. 单选按钮选择
3. 复选框选择
4. 按钮点击
5. 表单提交

使用httpbin.org作为测试网站，逐步验证每个交互功能。
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_text_input():
    """
    测试文本输入功能
    """
    
    print("⌨️ 文本输入测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        task_description = """
        请执行文本输入测试：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 在客户姓名字段输入 "测试用户"
        3. 在电话字段输入 "13800138000"
        4. 在邮箱字段输入 "<EMAIL>"
        5. 截取填写后的截图
        
        请逐步执行并报告每个步骤的结果。
        """
        
        print("\n🚀 开始文本输入测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 文本输入测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 文本输入测试失败: {e}")
        return False


def test_radio_button():
    """
    测试单选按钮功能
    """
    
    print("🔘 单选按钮测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        task_description = """
        请执行单选按钮测试：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 找到Pizza Size部分的单选按钮
        3. 选择 "Large" 选项（这是一个单选按钮，不是下拉菜单）
        4. 截取选择后的截图
        
        注意：Pizza Size是通过单选按钮(radio button)实现的，不是下拉菜单。
        """
        
        print("\n🚀 开始单选按钮测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 单选按钮测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 单选按钮测试失败: {e}")
        return False


def test_checkbox():
    """
    测试复选框功能
    """
    
    print("☑️ 复选框测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        task_description = """
        请执行复选框测试：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 找到Pizza Toppings部分的复选框
        3. 选择 "Bacon" 复选框
        4. 选择 "Extra Cheese" 复选框
        5. 截取选择后的截图
        
        注意：这些是复选框(checkbox)，可以选择多个选项。
        """
        
        print("\n🚀 开始复选框测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 复选框测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 复选框测试失败: {e}")
        return False


def test_form_submission():
    """
    测试表单提交功能
    """
    
    print("📤 表单提交测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        task_description = """
        请执行完整的表单提交测试：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 填写所有必要字段：
           - 客户姓名: "张三"
           - 电话: "13800138000"
           - 邮箱: "<EMAIL>"
           - 选择Pizza Size: "Large"
           - 选择配料: "Bacon"
           - 配送说明: "请在门口放置"
        3. 点击提交按钮
        4. 截取提交结果页面的截图
        
        请逐步执行并详细报告每个步骤。
        """
        
        print("\n🚀 开始表单提交测试...")
        result = crawler_agent.invoke(task_description)
        
        print("\n📊 表单提交测试结果：")
        print(result['output'])
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 表单提交测试失败: {e}")
        return False


def main():
    """主函数"""
    
    print("🎮 简单交互动作测试")
    print("=" * 60)
    print("这个测试将逐步验证基本的交互功能：")
    print("• ⌨️ 文本输入")
    print("• 🔘 单选按钮")
    print("• ☑️ 复选框")
    print("• 📤 表单提交")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 文本输入测试")
    print("2. 单选按钮测试")
    print("3. 复选框测试")
    print("4. 表单提交测试")
    print("5. 全部测试")
    
    choice = input("\n请输入选择 (1/2/3/4/5): ").strip()
    
    results = []
    
    if choice == "1":
        result = test_text_input()
        results.append(("文本输入测试", result))
    elif choice == "2":
        result = test_radio_button()
        results.append(("单选按钮测试", result))
    elif choice == "3":
        result = test_checkbox()
        results.append(("复选框测试", result))
    elif choice == "4":
        result = test_form_submission()
        results.append(("表单提交测试", result))
    elif choice == "5":
        print("\n🎮 执行全部测试...")
        
        print("\n1️⃣ 文本输入测试...")
        result1 = test_text_input()
        results.append(("文本输入测试", result1))
        
        print("\n2️⃣ 单选按钮测试...")
        result2 = test_radio_button()
        results.append(("单选按钮测试", result2))
        
        print("\n3️⃣ 复选框测试...")
        result3 = test_checkbox()
        results.append(("复选框测试", result3))
        
        print("\n4️⃣ 表单提交测试...")
        result4 = test_form_submission()
        results.append(("表单提交测试", result4))
    else:
        print("👋 无效选择，执行文本输入测试...")
        result = test_text_input()
        results.append(("文本输入测试", result))
    
    # 总结测试结果
    print("\n" + "="*60)
    print("🎉 简单交互测试总结")
    print("="*60)
    
    success_count = 0
    for test_name, result in results:
        if result:
            print(f"✅ {test_name}: 成功")
            success_count += 1
        else:
            print(f"❌ {test_name}: 失败")
    
    print(f"\n📊 测试结果: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有交互测试完成！")
        print("\n💡 验证的交互功能：")
        print("• ✅ 文本字段输入 - 姓名、电话、邮箱等")
        print("• ✅ 单选按钮选择 - Pizza大小选择")
        print("• ✅ 复选框选择 - 配料多选")
        print("• ✅ 表单提交 - 完整流程测试")
        print("• ✅ 页面截图 - 每步操作记录")
        
        return 0
    else:
        print(f"\n⚠️ 部分测试失败，请检查日志")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
