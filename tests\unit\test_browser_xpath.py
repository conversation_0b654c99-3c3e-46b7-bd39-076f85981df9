#!/usr/bin/env python3
"""
直接测试Browser类的XPath处理
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.core.browser import Browser
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_browser_xpath():
    """
    直接测试Browser类的XPath处理
    """
    
    print("🔧 测试Browser类XPath处理")
    print("=" * 50)
    
    try:
        # 创建Browser实例
        print("\n🌐 创建Browser实例...")
        browser = Browser(headless=False)  # 使用非headless模式便于观察
        
        # 导航到Google
        print("📍 导航到Google...")
        browser.navigate("https://www.google.com/")
        
        # 测试不同的XPath格式
        test_selectors = [
            "html/body/div[2]/div[4]/form/div[1]/div[1]/div[1]/div[1]/div[2]/textarea",  # 原始格式
            "xpath/html/body/div[2]/div[4]/form/div[1]/div[1]/div[1]/div[1]/div[2]/textarea",  # 带前缀
            "//textarea[@name='q']",  # 标准XPath
            "/html/body/div[2]/div[4]/form/div[1]/div[1]/div[1]/div[1]/div[2]/textarea"  # 绝对路径
        ]
        
        for i, selector in enumerate(test_selectors, 1):
            print(f"\n🧪 测试选择器 {i}: {selector}")
            try:
                # 尝试获取locator
                locator = browser._get_locator(selector)
                print(f"✅ 成功创建locator: {locator}")
                
                # 尝试检查元素是否存在
                try:
                    browser.wait_for_element(selector, timeout=5000)
                    print(f"✅ 元素存在且可定位")
                    
                    # 尝试输入文本
                    browser.type_text(selector, f"测试文本{i}")
                    print(f"✅ 成功输入文本")
                    
                    # 清空文本
                    browser.type_text(selector, "")
                    print(f"✅ 成功清空文本")
                    
                except Exception as e:
                    print(f"⚠️ 元素操作失败: {e}")
                    
            except Exception as e:
                print(f"❌ 选择器失败: {e}")
        
        # 截图
        print("\n📸 截取测试截图...")
        screenshot_path = browser.screenshot("xpath_test.png")
        print(f"✅ 截图保存到: {screenshot_path}")
        
        # 清理
        browser.close()
        print("\n✅ Browser清理完成")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Browser XPath test error: {e}")
        return False


if __name__ == "__main__":
    result = test_browser_xpath()
    if result:
        print("\n🎉 Browser XPath测试成功！")
    else:
        print("\n❌ Browser XPath测试失败")
