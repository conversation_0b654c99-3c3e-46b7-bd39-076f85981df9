"""
Test script to verify improved screenshot saving functionality with URL info.
"""

import os
import sys
import time

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.core.browser import <PERSON><PERSON><PERSON>


def test_improved_screenshot_naming():
    """Test improved screenshot naming with URL information."""
    print("🧪 Testing improved screenshot naming with URL info...")
    
    browser = Browser(headless=True)
    
    try:
        # Test 1: Simple domain
        print("\n📍 Test 1: Simple domain (httpbin.org)")
        result = browser.navigate("https://httpbin.org/html")
        print(f"✅ Navigation result: {result}")
        
        screenshot_path = browser.screenshot()
        print(f"📁 Screenshot saved to: {screenshot_path}")
        print(f"📂 Full path: {os.path.abspath(screenshot_path)}")
        
        # Verify file exists and naming
        if os.path.exists(screenshot_path):
            filename = os.path.basename(screenshot_path)
            print(f"✅ File exists: {filename}")
            if "httpbin_org" in filename:
                print("✅ URL info included in filename")
            else:
                print("❌ URL info missing from filename")
        
        time.sleep(1)  # Ensure different timestamp
        
        # Test 2: Domain with path
        print("\n📍 Test 2: Domain with path (httpbin.org/json)")
        result = browser.navigate("https://httpbin.org/json")
        print(f"✅ Navigation result: {result}")
        
        screenshot_path2 = browser.screenshot()
        print(f"📁 Screenshot saved to: {screenshot_path2}")
        
        if os.path.exists(screenshot_path2):
            filename2 = os.path.basename(screenshot_path2)
            print(f"✅ File exists: {filename2}")
            if "httpbin_org_json" in filename2:
                print("✅ URL path info included in filename")
            else:
                print("❌ URL path info missing from filename")
        
        time.sleep(1)
        
        # Test 3: Different domain
        print("\n📍 Test 3: Different domain (google.com)")
        result = browser.navigate("https://www.google.com")
        print(f"✅ Navigation result: {result}")
        
        screenshot_path3 = browser.screenshot()
        print(f"📁 Screenshot saved to: {screenshot_path3}")
        
        if os.path.exists(screenshot_path3):
            filename3 = os.path.basename(screenshot_path3)
            print(f"✅ File exists: {filename3}")
            if "google_com" in filename3:
                print("✅ Google domain info included in filename")
            else:
                print("❌ Google domain info missing from filename")
        
        time.sleep(1)
        
        # Test 4: Complex URL with query parameters
        print("\n📍 Test 4: Complex URL (httpbin.org/get?param=value)")
        result = browser.navigate("https://httpbin.org/get?param=value")
        print(f"✅ Navigation result: {result}")
        
        screenshot_path4 = browser.screenshot()
        print(f"📁 Screenshot saved to: {screenshot_path4}")
        
        if os.path.exists(screenshot_path4):
            filename4 = os.path.basename(screenshot_path4)
            print(f"✅ File exists: {filename4}")
            if "httpbin_org_get" in filename4:
                print("✅ URL path info included (query params ignored)")
            else:
                print("❌ URL path info missing from filename")
        
        print("\n📋 Summary of generated filenames:")
        print(f"  1. Simple domain: {os.path.basename(screenshot_path)}")
        print(f"  2. With path: {os.path.basename(screenshot_path2)}")
        print(f"  3. Google: {os.path.basename(screenshot_path3)}")
        print(f"  4. Complex URL: {os.path.basename(screenshot_path4)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        browser.close()
        print("🔒 Browser closed")


def test_fallback_naming():
    """Test fallback naming when URL processing fails."""
    print("\n🧪 Testing fallback naming...")
    
    browser = Browser(headless=True)
    
    try:
        # Navigate to a page first
        result = browser.navigate("https://httpbin.org/html")
        print(f"✅ Navigation result: {result}")
        
        # Test with custom path (should still work)
        custom_path = "../../screenshots/test_custom_naming.png"
        screenshot_path = browser.screenshot(custom_path)
        print(f"📁 Custom path screenshot: {screenshot_path}")
        
        if os.path.exists(screenshot_path):
            print("✅ Custom path naming works")
        else:
            print("❌ Custom path naming failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Fallback test failed: {e}")
        return False
        
    finally:
        browser.close()


def list_all_screenshots():
    """List all screenshots in the directory."""
    print("\n📁 All screenshots in directory:")
    screenshots_dir = "../../screenshots"
    
    if os.path.exists(screenshots_dir):
        files = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
        files.sort()
        
        for file in files:
            file_path = os.path.join(screenshots_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"  - {file} ({file_size} bytes)")
    else:
        print("  Screenshots directory not found")


if __name__ == "__main__":
    print("🚀 Starting improved screenshot naming tests...\n")
    
    # Test improved naming
    success1 = test_improved_screenshot_naming()
    
    # Test fallback
    success2 = test_fallback_naming()
    
    # List all screenshots
    list_all_screenshots()
    
    if success1 and success2:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
