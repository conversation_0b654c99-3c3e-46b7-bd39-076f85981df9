"""
MCP工具的JSON Schema定义

定义了MCP协议中使用的工具参数和响应格式的JSON Schema。
"""

from typing import Dict, Any

# intelligent_web_task工具的Schema
INTELLIGENT_WEB_TASK_SCHEMA = {
    "type": "object",
    "properties": {
        "task_description": {
            "type": "string",
            "description": "自然语言描述的网页任务，例如：'打开google.com并搜索Python'",
            "minLength": 1,
            "maxLength": 1000
        }
    },
    "required": ["task_description"],
    "additionalProperties": False
}

# browser_status工具的Schema
BROWSER_STATUS_SCHEMA = {
    "type": "object",
    "properties": {},
    "additionalProperties": False
}

# take_screenshot工具的Schema
TAKE_SCREENSHOT_SCHEMA = {
    "type": "object",
    "properties": {
        "filename": {
            "type": "string",
            "description": "截图文件名，默认为screenshot.png",
            "default": "screenshot.png",
            "pattern": r"^[a-zA-Z0-9_\-\.]+\.(png|jpg|jpeg)$"
        }
    },
    "additionalProperties": False
}

# cleanup_browser工具的Schema
CLEANUP_BROWSER_SCHEMA = {
    "type": "object",
    "properties": {},
    "additionalProperties": False
}

# 所有工具Schema的映射
TOOL_SCHEMAS: Dict[str, Dict[str, Any]] = {
    "intelligent_web_task": INTELLIGENT_WEB_TASK_SCHEMA,
    "browser_status": BROWSER_STATUS_SCHEMA,
    "take_screenshot": TAKE_SCREENSHOT_SCHEMA,
    "cleanup_browser": CLEANUP_BROWSER_SCHEMA
}

def get_tool_schema(tool_name: str) -> Dict[str, Any]:
    """
    获取指定工具的JSON Schema
    
    Args:
        tool_name: 工具名称
        
    Returns:
        工具的JSON Schema字典
        
    Raises:
        KeyError: 如果工具名称不存在
    """
    if tool_name not in TOOL_SCHEMAS:
        raise KeyError(f"Unknown tool: {tool_name}")
    
    return TOOL_SCHEMAS[tool_name].copy()

def validate_tool_arguments(tool_name: str, arguments: Dict[str, Any]) -> bool:
    """
    验证工具参数是否符合Schema
    
    Args:
        tool_name: 工具名称
        arguments: 工具参数
        
    Returns:
        是否验证通过
    """
    try:
        schema = get_tool_schema(tool_name)
        # 这里可以添加更详细的JSON Schema验证
        # 目前只做基本的必需参数检查
        
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in arguments:
                return False
                
        return True
    except KeyError:
        return False
