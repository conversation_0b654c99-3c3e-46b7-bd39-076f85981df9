# 安装指南

## 📋 系统要求

### 最低要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python**: 3.9 或更高版本
- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Windows 11, macOS 12+, Ubuntu 20.04+
- **Python**: 3.11 或更高版本
- **内存**: 8GB RAM 或更多
- **存储**: 10GB 可用空间
- **网络**: 高速互联网连接

## 🚀 快速安装

### 方法1: 使用 pip 安装 (推荐)

```bash
# 1. 克隆仓库
git clone https://github.com/your-org/iicrawlermcp.git
cd iicrawlermcp

# 2. 创建虚拟环境
python -m venv .venv

# 3. 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 安装项目
pip install -e .
```

### 方法2: 使用 uv 安装 (更快)

```bash
# 1. 安装 uv (如果还没有)
pip install uv

# 2. 克隆仓库
git clone https://github.com/your-org/iicrawlermcp.git
cd iicrawlermcp

# 3. 使用 uv 安装
uv sync

# 4. 激活环境
source .venv/bin/activate  # macOS/Linux
.venv\Scripts\activate     # Windows
```

### 方法3: 使用 Docker

```bash
# 1. 克隆仓库
git clone https://github.com/your-org/iicrawlermcp.git
cd iicrawlermcp

# 2. 构建 Docker 镜像
docker build -t iicrawlermcp .

# 3. 运行容器
docker run -p 8000:8000 iicrawlermcp
```

## ⚙️ 详细配置

### 环境变量配置

创建 `.env` 文件：

```bash
# API 配置
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/iicrawler
REDIS_URL=redis://localhost:6379/0

# 浏览器配置
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1
```

### 数据库设置

#### PostgreSQL (推荐)

```bash
# 1. 安装 PostgreSQL
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS (使用 Homebrew)
brew install postgresql

# Windows
# 下载并安装 PostgreSQL 官方安装包

# 2. 创建数据库
sudo -u postgres createdb iicrawler

# 3. 创建用户
sudo -u postgres createuser --interactive
```

#### Redis

```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS (使用 Homebrew)
brew install redis

# Windows
# 下载并安装 Redis for Windows
```

### 浏览器配置

#### Playwright 浏览器安装

```bash
# 安装浏览器
playwright install

# 仅安装 Chromium (推荐)
playwright install chromium

# 安装系统依赖 (Linux)
playwright install-deps
```

## 🔧 开发环境设置

### 开发依赖安装

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 或使用 uv
uv sync --dev
```

### 代码质量工具

```bash
# 安装 pre-commit hooks
pre-commit install

# 运行代码格式化
black src/ tests/
isort src/ tests/

# 运行类型检查
mypy src/

# 运行测试
pytest tests/
```

## 🧪 验证安装

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/
pytest tests/integration/

# 运行带覆盖率的测试
pytest --cov=src/iicrawlermcp
```

### 启动服务

```bash
# 启动开发服务器
python main.py

# 或使用 uvicorn
uvicorn src.iicrawlermcp.main:app --reload

# 检查服务状态
curl http://localhost:8000/health
```

### 运行示例

```bash
# 运行基础示例
python examples/basic/simple_crawl.py

# 运行高级示例
python examples/advanced/multi_agent_crawl.py
```

## 🐛 常见问题

### 安装问题

**Q: pip install 失败，提示权限错误**
```bash
# 解决方案：使用用户安装
pip install --user -r requirements.txt
```

**Q: Playwright 浏览器下载失败**
```bash
# 解决方案：使用代理或手动下载
PLAYWRIGHT_DOWNLOAD_HOST=https://playwright.azureedge.net playwright install
```

**Q: 数据库连接失败**
```bash
# 检查数据库服务状态
sudo systemctl status postgresql
sudo systemctl status redis

# 重启服务
sudo systemctl restart postgresql
sudo systemctl restart redis
```

### 运行时问题

**Q: 浏览器启动失败**
- 检查是否安装了必要的系统依赖
- 尝试使用有头模式：`BROWSER_HEADLESS=false`

**Q: 内存不足错误**
- 增加系统内存或使用交换文件
- 减少并发Agent数量

**Q: 网络连接超时**
- 检查网络连接
- 增加超时时间配置

## 🔄 更新升级

### 更新到最新版本

```bash
# 拉取最新代码
git pull origin main

# 更新依赖
pip install -r requirements.txt --upgrade

# 或使用 uv
uv sync --upgrade

# 运行数据库迁移
python -m alembic upgrade head
```

### 版本回退

```bash
# 查看版本历史
git log --oneline

# 回退到特定版本
git checkout <commit-hash>

# 重新安装依赖
pip install -r requirements.txt
```

## 📞 获取帮助

如果遇到安装问题，可以：

1. 查看 [故障排除指南](troubleshooting.md)
2. 搜索 [GitHub Issues](https://github.com/your-org/iicrawlermcp/issues)
3. 提交新的 Issue
4. 加入社区讨论

---

*最后更新: 2025-01-29*
