#!/usr/bin/env python3
"""
测试高亮框清理功能
"""

import sys
import os
import time

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_highlight_cleanup():
    """
    测试高亮框清理功能
    """
    
    print("🧹 高亮框清理功能测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 步骤1：导航到测试页面
        print("\n1️⃣ 导航到测试页面...")
        task1 = """
        导航到 https://httpbin.org/forms/post 并等待页面加载完成。
        """
        result1 = crawler_agent.invoke(task1)
        print("✅ 页面导航完成")
        
        # 步骤2：第一次DOM分析（会产生高亮框）
        print("\n2️⃣ 第一次DOM分析...")
        task2 = """
        分析页面上的所有表单元素，这会在页面上添加彩色高亮框。
        """
        result2 = crawler_agent.invoke(task2)
        print("✅ 第一次DOM分析完成（应该有高亮框）")
        
        # 等待一下让用户观察
        print("\n⏱️ 等待3秒，让您观察页面上的高亮框...")
        time.sleep(3)
        
        # 步骤3：第二次DOM分析（会产生更多高亮框）
        print("\n3️⃣ 第二次DOM分析...")
        task3 = """
        再次分析页面上的交互元素，查找所有可点击的元素。
        """
        result3 = crawler_agent.invoke(task3)
        print("✅ 第二次DOM分析完成（应该有更多高亮框）")
        
        # 等待一下让用户观察
        print("\n⏱️ 等待3秒，让您观察页面上累积的高亮框...")
        time.sleep(3)
        
        # 步骤4：清理高亮框
        print("\n4️⃣ 清理高亮框...")
        task4 = """
        清理页面上的所有高亮框，使用dom_clear_highlights工具。
        """
        result4 = crawler_agent.invoke(task4)
        print("✅ 高亮框清理完成")
        
        # 等待一下让用户观察
        print("\n⏱️ 等待3秒，让您观察清理后的页面...")
        time.sleep(3)
        
        # 步骤5：验证清理效果
        print("\n5️⃣ 验证清理效果...")
        task5 = """
        截取当前页面的截图，验证高亮框是否已被清理。
        """
        result5 = crawler_agent.invoke(task5)
        print("✅ 清理效果验证完成")
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"Highlight cleanup test error: {e}")
        return False


def test_automatic_cleanup():
    """
    测试自动清理功能
    """
    
    print("🔄 自动清理功能测试")
    print("=" * 50)
    
    try:
        config.validate()
        print("✅ 配置验证通过")
        
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 测试多次DOM分析，验证自动清理
        print("\n🔄 执行多次DOM分析，验证自动清理...")
        
        for i in range(3):
            print(f"\n第{i+1}次DOM分析...")
            task = f"""
            导航到 https://httpbin.org/forms/post 并分析页面元素。
            这是第{i+1}次分析，应该自动清理之前的高亮框。
            """
            result = crawler_agent.invoke(task)
            print(f"✅ 第{i+1}次分析完成")
            
            # 短暂等待
            time.sleep(2)
        
        print("\n📸 最终截图...")
        final_task = """
        截取最终的页面截图，验证只有最后一次分析的高亮框存在。
        """
        final_result = crawler_agent.invoke(final_task)
        print("✅ 最终截图完成")
        
        crawler_agent.cleanup()
        return True
        
    except Exception as e:
        print(f"\n❌ 自动清理测试失败: {e}")
        logger.error(f"Automatic cleanup test error: {e}")
        return False


def main():
    """主函数"""
    
    print("🧹 高亮框清理功能测试")
    print("=" * 60)
    print("这个测试将验证DOM分析时高亮框的清理功能")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 手动清理测试（演示问题和解决方案）")
    print("2. 自动清理测试（验证自动清理机制）")
    
    choice = input("\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        print("\n🧹 执行手动清理测试...")
        result = test_highlight_cleanup()
    elif choice == "2":
        print("\n🔄 执行自动清理测试...")
        result = test_automatic_cleanup()
    else:
        print("👋 无效选择，执行手动清理测试...")
        result = test_highlight_cleanup()
    
    if result:
        print("\n🎉 高亮框清理测试成功！")
        print("\n💡 修复的问题：")
        print("• ✅ 高亮框累积问题 - 每次DOM分析前自动清理")
        print("• ✅ 手动清理工具 - dom_clear_highlights工具")
        print("• ✅ 自动清理机制 - JavaScript中的cleanupHighlights函数")
        print("• ✅ 内存管理 - 清理事件监听器和DOM元素")
        
        return 0
    else:
        print("\n❌ 高亮框清理测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
