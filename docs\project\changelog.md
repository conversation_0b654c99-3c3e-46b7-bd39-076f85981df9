# 更新日志

本文档记录了 iICrawlerMCP 项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 多智能体架构设计和实现
- BrowserAgent - 专门的浏览器控制智能体
- CrawlerAgent - 主协调智能体
- 完整的DOM提取器模块
- 统一的截图路径管理
- 标准化的项目目录结构
- 完整的文档体系

### 改进
- 重新组织了测试和示例目录
- 优化了截图保存逻辑
- 改进了配置管理系统
- 增强了错误处理和日志记录

### 修复
- 修复了截图路径混乱问题
- 解决了目录结构不规范问题
- 修复了导入路径错误

### 文档
- 创建了完整的文档导航系统
- 添加了快速开始指南
- 完善了API参考文档
- 增加了架构设计文档

## [0.1.0] - 2025-07-26

### 新增
- 初始项目结构
- 基础的LangChain集成
- Playwright浏览器自动化
- 基本的配置管理
- 初始测试套件

### 特性
- 支持网页导航和截图
- 基础的元素交互功能
- 环境变量配置
- 简单的错误处理

---

## 版本说明

- **新增**: 新功能
- **改进**: 对现有功能的改进
- **修复**: 错误修复
- **文档**: 文档相关变更
- **安全**: 安全相关修复
- **废弃**: 即将移除的功能
- **移除**: 已移除的功能
