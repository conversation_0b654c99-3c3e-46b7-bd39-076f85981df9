# Tests Directory

This directory contains all tests for the iICrawlerMCP project.

## Structure

- `unit/` - Unit tests for individual components
- `integration/` - Integration tests for component interactions  
- `e2e/` - End-to-end tests for complete workflows

## Running Tests

```bash
# Run all tests
pytest tests/

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# Run with coverage
pytest tests/ --cov=src/iicrawlermcp
```

## Test Guidelines

- Unit tests should test individual functions/classes in isolation
- Integration tests should test component interactions
- E2E tests should test complete user workflows
- All tests should be independent and repeatable
