# iICrawlerMCP 开发指南

> 为贡献者和开发者提供的完整开发指南

## 🚀 开发环境搭建

### 前置要求
- Python 3.11+
- Node.js 16+ (用于Playwright)
- Git
- OpenAI API Key

### 快速搭建
```bash
# 1. 克隆项目
git clone https://github.com/your-username/iicrawlermcp.git
cd iicrawlermcp

# 2. 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 3. 安装开发依赖
pip install -r requirements-dev.txt
playwright install

# 4. 配置环境
cp .env.example .env
# 编辑 .env 文件，添加你的 OPENAI_API_KEY

# 5. 验证安装
pytest tests/ -v
python examples/basic/browser_basics.py
```

## 🏗️ 项目架构理解

### 核心设计原则
1. **多Agent协作**: 专业化分工，智能协作
2. **工具分层**: 基础工具 + 高级工具的清晰分层
3. **统一接口**: 标准化的API设计
4. **可扩展性**: 易于添加新Agent和工具

### 代码组织
```
src/iicrawlermcp/
├── agents/          # Agent实现
├── tools/           # 工具集合
├── core/            # 核心引擎
└── dom/             # DOM处理
```

### 关键组件
- **Agent**: 智能任务执行单元
- **Tool**: 具体功能实现
- **Engine**: 核心服务引擎
- **Extractor**: 数据提取器

## 🧪 测试策略

### 测试分类
```bash
# 单元测试 - 测试单个函数/类
pytest tests/unit/ -v

# 集成测试 - 测试组件交互
pytest tests/integration/ -v

# 端到端测试 - 测试完整流程
pytest tests/e2e/ -v

# 性能测试 - 测试性能指标
pytest tests/performance/ -v
```

### 测试覆盖率
```bash
# 生成覆盖率报告
pytest tests/ --cov=src/iicrawlermcp --cov-report=html

# 查看覆盖率报告
open htmlcov/index.html
```

### 编写测试
```python
# tests/unit/test_browser_tools.py
import pytest
from iicrawlermcp.tools.browser_tools import BrowserToolkit

def test_browser_toolkit_basic_tools():
    """测试基础工具获取"""
    tools = BrowserToolkit.get_basic_tools()
    assert len(tools) == 6
    assert all(hasattr(tool, 'name') for tool in tools)

def test_browser_toolkit_advanced_tools():
    """测试高级工具获取"""
    tools = BrowserToolkit.get_advanced_tools()
    assert len(tools) == 10
    assert all(hasattr(tool, 'name') for tool in tools)
```

## 🔧 代码规范

### 代码风格
```bash
# 代码格式化
black src/ tests/ examples/

# 导入排序
isort src/ tests/ examples/

# 代码检查
flake8 src/ tests/

# 类型检查
mypy src/
```

### 提交规范
```bash
# 提交消息格式
<type>(<scope>): <description>

# 类型说明
feat:     新功能
fix:      Bug修复
docs:     文档更新
style:    代码格式
refactor: 重构
test:     测试相关
chore:    构建/工具相关

# 示例
feat(agents): add new ElementAgent for DOM analysis
fix(browser): resolve screenshot path issue
docs(api): update API reference documentation
```

### 代码审查清单
- [ ] 代码符合项目风格规范
- [ ] 包含适当的测试用例
- [ ] 文档字符串完整准确
- [ ] 类型提示正确
- [ ] 错误处理完善
- [ ] 性能考虑合理

## 🤖 添加新Agent

### 1. 创建Agent类
```python
# src/iicrawlermcp/agents/new_agent.py
from typing import List, Optional
from langchain_core.tools import BaseTool
from langchain.agents import create_openai_functions_agent
from ..core.config import config

class NewAgent:
    """新Agent的描述"""
    
    def __init__(self, tools: Optional[List[BaseTool]] = None):
        self.tools = tools or self._get_agent_tools()
        self.agent = self._create_agent()
    
    def _get_agent_tools(self) -> List[BaseTool]:
        """获取Agent专用工具"""
        # 实现工具获取逻辑
        pass
    
    def _create_agent(self):
        """创建LangChain Agent"""
        # 实现Agent创建逻辑
        pass
    
    def invoke(self, task: str) -> dict:
        """执行任务"""
        # 实现任务执行逻辑
        pass
    
    def cleanup(self):
        """清理资源"""
        # 实现资源清理逻辑
        pass

def build_new_agent(**kwargs) -> NewAgent:
    """构建新Agent的工厂函数"""
    return NewAgent(**kwargs)
```

### 2. 添加工具集
```python
# src/iicrawlermcp/tools/new_agent_tools.py
from langchain_core.tools import tool

@tool
def new_agent_tool(param: str) -> str:
    """新Agent专用工具"""
    try:
        # 实现工具逻辑
        return "操作成功"
    except Exception as e:
        return f"❌ 操作失败: {str(e)}"

def get_new_agent_tools() -> List[BaseTool]:
    """获取新Agent工具集"""
    return [new_agent_tool]
```

### 3. 更新导出
```python
# src/iicrawlermcp/agents/__init__.py
from .new_agent import NewAgent, build_new_agent

__all__ = [
    # ... 其他导出
    'NewAgent',
    'build_new_agent',
]
```

### 4. 添加测试
```python
# tests/unit/test_new_agent.py
import pytest
from iicrawlermcp.agents import build_new_agent

def test_new_agent_creation():
    """测试新Agent创建"""
    agent = build_new_agent()
    assert agent is not None
    assert hasattr(agent, 'invoke')
    assert hasattr(agent, 'cleanup')

def test_new_agent_invoke():
    """测试新Agent任务执行"""
    agent = build_new_agent()
    result = agent.invoke("测试任务")
    assert 'output' in result
    agent.cleanup()
```

## 🔧 添加新工具

### 1. 创建工具函数
```python
# src/iicrawlermcp/tools/category_tools.py
from langchain_core.tools import tool
from typing import Optional

@tool
def new_tool(param1: str, param2: Optional[int] = None) -> str:
    """
    新工具的描述
    
    Args:
        param1: 参数1描述
        param2: 参数2描述（可选）
        
    Returns:
        操作结果描述
        
    Example:
        new_tool("example", 42)
    """
    try:
        # 实现工具逻辑
        result = f"处理 {param1} 成功"
        return result
    except Exception as e:
        error_msg = f"工具执行失败: {str(e)}"
        return f"❌ {error_msg}"
```

### 2. 添加到工具包
```python
# 更新相应的工具包类
class ToolkitClass:
    @classmethod
    def get_tools(cls) -> List[BaseTool]:
        return [
            # ... 现有工具
            new_tool,
        ]
```

### 3. 编写工具测试
```python
# tests/unit/test_new_tool.py
def test_new_tool_success():
    """测试工具成功执行"""
    result = new_tool("test_param")
    assert "成功" in result
    assert not result.startswith("❌")

def test_new_tool_error_handling():
    """测试工具错误处理"""
    # 测试错误情况
    pass
```

## 📊 性能优化

### 1. 性能监控
```python
import time
import logging
from functools import wraps

def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logging.info(f"{func.__name__} 执行时间: {execution_time:.2f}秒")
        
        return result
    return wrapper
```

### 2. 内存优化
- 及时清理不需要的对象
- 使用生成器处理大数据集
- 合理使用缓存机制

### 3. 浏览器优化
- 复用浏览器实例
- 关闭不需要的标签页
- 禁用不必要的浏览器功能

## 🐛 调试技巧

### 1. 日志配置
```python
import logging

# 开发环境日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 设置详细模式
agent = build_agent(verbose=True)
```

### 2. 断点调试
```python
import pdb

def debug_function():
    # 设置断点
    pdb.set_trace()
    
    # 或使用 breakpoint() (Python 3.7+)
    breakpoint()
```

### 3. 浏览器调试
```python
# 启用可视化浏览器
browser = get_global_browser(headless=False)

# 添加延时观察
import time
time.sleep(5)  # 暂停5秒观察页面状态
```

## 📈 项目发展历程

### 已完成里程碑
- ✅ **阶段1**: 核心基础设施 (2024年1-2月)
- ✅ **阶段2**: 基础Agent实现 (2024年2-3月)
- ✅ **阶段3**: 专业化Agent扩展 (2024年3-4月)
- ✅ **阶段4**: 工具重构和优化 (2024年4月-2025年7月)

### 当前状态
- 📊 **完成度**: 80% (4/5 阶段完成)
- 🤖 **Agent数量**: 3个核心Agent
- 🔧 **工具数量**: 16个浏览器工具 + 10个DOM工具
- 🧪 **测试覆盖**: 单元测试、集成测试、端到端测试

### 下一步计划
- 🚀 **阶段5**: 高级功能和优化
  - 性能优化和缓存机制
  - 安全和反爬虫策略
  - 数据提取和处理增强
  - 插件系统和扩展接口

## 🤝 贡献流程

### 1. 准备工作
```bash
# Fork项目到你的GitHub账户
# 克隆你的Fork
git clone https://github.com/your-username/iicrawlermcp.git
cd iicrawlermcp

# 添加上游仓库
git remote add upstream https://github.com/original-owner/iicrawlermcp.git
```

### 2. 开发流程
```bash
# 创建功能分支
git checkout -b feature/amazing-feature

# 进行开发
# ... 编写代码、测试 ...

# 提交更改
git add .
git commit -m "feat: add amazing feature"

# 推送到你的Fork
git push origin feature/amazing-feature

# 创建Pull Request
```

### 3. Pull Request要求
- 清晰的PR描述
- 包含相关测试
- 通过所有CI检查
- 代码审查通过

## 📞 获取帮助

### 开发问题
- 📖 查看 [ARCHITECTURE.md](ARCHITECTURE.md) 了解系统设计
- 📋 查看 [API_REFERENCE.md](API_REFERENCE.md) 了解API详情
- 💡 参考 [examples/](examples/) 中的示例代码

### 社区支持
- 🐛 [GitHub Issues](https://github.com/your-username/iicrawlermcp/issues) - 报告Bug
- 💬 [GitHub Discussions](https://github.com/your-username/iicrawlermcp/discussions) - 讨论和提问
- 📧 Email: <EMAIL>

---

*感谢你对iICrawlerMCP项目的贡献！每一个贡献都让这个项目变得更好。*
