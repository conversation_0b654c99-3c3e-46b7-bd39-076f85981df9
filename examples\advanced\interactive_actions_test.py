#!/usr/bin/env python3
"""
交互动作测试示例

这个示例演示了如何使用CrawlerAgent执行各种交互动作：
1. 点击按钮和链接
2. 填写表单字段
3. 选择下拉菜单
4. 处理复选框和单选框
5. 日期选择器操作

使用httpbin.org作为测试网站，因为它提供了各种表单元素用于测试。
"""

import sys
import os

# Add the src directory to the path so we can import our package
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from iicrawlermcp.agents import build_agent
from iicrawlermcp.core.config import config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_form_interactions():
    """
    测试表单交互功能
    """
    
    print("📝 表单交互测试")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 表单填写任务
        task_description = """
        请执行以下表单交互测试：
        
        1. 导航到 https://httpbin.org/forms/post
        2. 等待页面加载完成
        3. 填写表单字段：
           - 在 "custname" 字段输入 "张三"
           - 在 "custtel" 字段输入 "13800138000"
           - 在 "custemail" 字段输入 "<EMAIL>"
           - 在 "size" 下拉菜单选择 "Large"
           - 选择 "bacon" 复选框
           - 在 "comments" 文本区域输入 "这是一个测试订单"
        4. 截取填写完成后的表单截图
        5. 点击提交按钮
        6. 截取提交结果页面的截图
        
        请详细报告每个步骤的执行情况。
        """
        
        print("\n🚀 开始执行表单交互测试...")
        print("📋 任务：测试各种表单元素的交互")
        
        # 执行任务
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 表单交互测试完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 表单交互测试失败: {e}")
        logger.error(f"Form interaction test error: {e}")
        return None


def test_click_actions():
    """
    测试点击动作
    """
    
    print("🖱️ 点击动作测试")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # 点击测试任务
        task_description = """
        请执行以下点击动作测试：
        
        1. 导航到 https://httpbin.org/
        2. 等待页面加载完成
        3. 找到并点击 "HTTP Methods" 部分的链接
        4. 尝试点击不同的HTTP方法链接（如GET、POST等）
        5. 对于每个点击的链接，截取页面截图
        6. 测试页面中的各种可点击元素
        
        请详细报告每个点击动作的结果。
        """
        
        print("\n🚀 开始执行点击动作测试...")
        print("📋 任务：测试各种点击操作")
        
        # 执行任务
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 点击动作测试完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ 点击动作测试失败: {e}")
        logger.error(f"Click action test error: {e}")
        return None


def test_html_form_elements():
    """
    测试HTML表单元素
    """
    
    print("🎛️ HTML表单元素测试")
    print("=" * 50)
    
    try:
        # 验证配置
        config.validate()
        print("✅ 配置验证通过")
        
        # 创建CrawlerAgent
        print("\n🤖 创建CrawlerAgent...")
        crawler_agent = build_agent()
        print("✅ CrawlerAgent创建成功")
        
        # HTML表单元素测试任务
        task_description = """
        请执行以下HTML表单元素测试：
        
        1. 导航到 https://httpbin.org/html
        2. 等待页面加载完成
        3. 分析页面上的所有交互元素
        4. 测试页面中的各种元素：
           - 查找所有链接并尝试点击
           - 查找所有按钮并测试点击
           - 查找表单字段并尝试输入
           - 查找选择框并尝试选择
        5. 对每种元素类型截取测试截图
        6. 报告发现的所有交互元素类型
        
        请详细分析页面结构并测试所有可交互元素。
        """
        
        print("\n🚀 开始执行HTML表单元素测试...")
        print("📋 任务：测试HTML页面中的各种表单元素")
        
        # 执行任务
        result = crawler_agent.invoke(task_description)
        
        print("\n" + "="*50)
        print("🎉 HTML表单元素测试完成！")
        print("="*50)
        print("\n📊 执行结果：")
        print(result['output'])
        
        # 清理资源
        crawler_agent.cleanup()
        print("\n✅ 资源清理完成")
        
        return result
        
    except Exception as e:
        print(f"\n❌ HTML表单元素测试失败: {e}")
        logger.error(f"HTML form elements test error: {e}")
        return None


def main():
    """主函数"""
    
    print("🎮 交互动作综合测试")
    print("=" * 60)
    print("这个演示将测试CrawlerAgent的各种交互能力：")
    print("• 📝 表单填写和提交")
    print("• 🖱️ 各种点击操作")
    print("• 🎛️ HTML表单元素处理")
    print("• 📸 每步操作的截图记录")
    print("• 🤖 智能元素识别和操作")
    print("=" * 60)
    
    # 询问用户选择测试类型
    print("\n请选择测试类型：")
    print("1. 表单交互测试（填写、选择、提交）")
    print("2. 点击动作测试（链接、按钮点击）")
    print("3. HTML表单元素测试（综合元素分析）")
    print("4. 全部测试")
    
    choice = input("\n请输入选择 (1/2/3/4): ").strip()
    
    results = []
    
    if choice == "1":
        print("\n📝 执行表单交互测试...")
        result = test_form_interactions()
        results.append(("表单交互测试", result))
    elif choice == "2":
        print("\n🖱️ 执行点击动作测试...")
        result = test_click_actions()
        results.append(("点击动作测试", result))
    elif choice == "3":
        print("\n🎛️ 执行HTML表单元素测试...")
        result = test_html_form_elements()
        results.append(("HTML表单元素测试", result))
    elif choice == "4":
        print("\n🎮 执行全部测试...")
        
        print("\n1️⃣ 表单交互测试...")
        result1 = test_form_interactions()
        results.append(("表单交互测试", result1))
        
        print("\n2️⃣ 点击动作测试...")
        result2 = test_click_actions()
        results.append(("点击动作测试", result2))
        
        print("\n3️⃣ HTML表单元素测试...")
        result3 = test_html_form_elements()
        results.append(("HTML表单元素测试", result3))
    else:
        print("👋 无效选择，执行表单交互测试...")
        result = test_form_interactions()
        results.append(("表单交互测试", result))
    
    # 总结测试结果
    print("\n" + "="*60)
    print("🎉 交互动作测试总结")
    print("="*60)
    
    success_count = 0
    for test_name, result in results:
        if result:
            print(f"✅ {test_name}: 成功")
            success_count += 1
        else:
            print(f"❌ {test_name}: 失败")
    
    print(f"\n📊 测试结果: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有交互动作测试完成！")
        print("\n💡 验证的功能：")
        print("• ✅ 表单字段填写 - 文本输入、下拉选择、复选框")
        print("• ✅ 点击操作 - 链接点击、按钮点击、元素交互")
        print("• ✅ 页面导航 - 自动导航和页面等待")
        print("• ✅ 元素识别 - 智能元素定位和分析")
        print("• ✅ 截图记录 - 每步操作的可视化记录")
        print("• ✅ 错误处理 - 智能错误恢复和重试")
        
        return 0
    else:
        print(f"\n⚠️ 部分测试失败，请检查日志")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
