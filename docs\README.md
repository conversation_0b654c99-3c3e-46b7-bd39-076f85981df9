# iICrawlerMCP 文档中心

欢迎来到 iICrawlerMCP 项目的文档中心！本目录包含了项目的所有技术文档，按照功能和用途进行了合理的分类组织。

## 📚 文档结构

### 🚀 快速开始
- [项目概述](../README.md) - 项目介绍和快速开始指南
- [安装指南](user-guide/installation.md) - 详细的安装和配置说明
- [快速上手](user-guide/quick-start.md) - 5分钟快速体验

### 🏗️ 架构设计
- [系统架构](architecture/system-architecture.md) - 整体系统架构设计
- [LangGraph分层监督架构](architecture/langgraph-hierarchical.md) - 推荐的新架构方案
- [架构对比分析](architecture/architecture-comparison.md) - 多种架构方案对比
- [迁移指南](architecture/migration-guide.md) - 从当前架构迁移到新架构

### 📖 用户指南
- [安装指南](user-guide/installation.md) - 详细的安装和配置说明 ✅
- [快速上手](user-guide/quick-start.md) - 5分钟快速体验 ✅
- [用户手册](user-guide/user-manual.md) - 完整的用户操作指南 ✅
- [API参考](user-guide/api-reference.md) - REST API 和 WebSocket API 文档 ✅
- [配置指南](user-guide/configuration.md) - 系统配置参数说明 ✅
- [故障排除](user-guide/troubleshooting.md) - 常见问题和解决方案 ✅

### 🔧 开发指南
- [开发环境搭建](development/setup.md) - 开发环境配置
- [代码贡献指南](development/contributing.md) - 如何参与项目开发
- [代码规范](development/coding-standards.md) - 代码风格和规范
- [测试指南](development/testing.md) - 单元测试和集成测试

### 📋 项目管理
- [项目状态和路线图](project/status-and-roadmap.md) - 当前状态和未来规划
- [实施路线图](project/implementation-roadmap.md) - 详细的实施计划
- [变更日志](project/changelog.md) - 版本更新记录
- [发布计划](project/release-plan.md) - 版本发布计划

### 🔍 技术深入
- [核心组件](technical/core-components.md) - 核心组件详细说明 ✅
- [Agent设计](technical/agent-design.md) - Agent架构和设计模式
- [状态管理](technical/state-management.md) - 状态管理机制
- [性能优化](technical/performance.md) - 性能优化策略
- [工具优化计划](technical/delegation-tools-optimization.md) - 委托工具优化方案 ✅

### 📊 运维指南
- [部署指南](operations/deployment.md) - 生产环境部署 ✅
- [监控告警](operations/monitoring.md) - 系统监控和告警配置
- [备份恢复](operations/backup-recovery.md) - 数据备份和恢复
- [安全配置](operations/security.md) - 安全配置和最佳实践

## 🎯 文档导航

### 按角色导航
- **新用户**: README.md → 安装指南 → 快速上手 → 用户手册
- **开发者**: 系统架构 → 开发环境搭建 → 代码贡献指南 → 技术深入
- **架构师**: 系统架构 → LangGraph分层监督架构 → 架构对比分析
- **运维人员**: 部署指南 → 监控告警 → 备份恢复 → 安全配置

### 按场景导航
- **项目评估**: 项目概述 → 系统架构 → 项目状态和路线图
- **技术选型**: 架构对比分析 → LangGraph分层监督架构 → 迁移指南
- **开发实施**: 开发环境搭建 → 核心组件 → Agent设计 → 测试指南
- **生产部署**: 部署指南 → 配置说明 → 监控告警 → 故障排除

## 📝 文档维护

### 文档更新原则
1. **及时性**: 代码变更后及时更新相关文档
2. **准确性**: 确保文档内容与实际代码保持一致
3. **完整性**: 重要功能都应有对应的文档说明
4. **可读性**: 使用清晰的结构和易懂的语言

### 贡献文档
如果您发现文档有错误或需要改进，欢迎：
1. 提交 Issue 报告问题
2. 提交 Pull Request 直接修改
3. 联系维护团队讨论改进建议

## 🔗 相关链接
- [Git 仓库](https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp)
- [问题反馈](https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp/issues)
- [合并请求](https://git.dev.sh.ctripcorp.com/octopus/icrawlermcp/merge_requests)
- [更新日志](project/changelog.md)

---

*最后更新: 2025-01-29*
